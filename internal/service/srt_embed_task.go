package service

import (
	"bufio"
	"context"
	"fmt"
	"go.uber.org/zap"
	"krillin-ai/internal/dto"
	"krillin-ai/internal/storage"
	"krillin-ai/internal/types"
	"krillin-ai/log"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
)

func (s Service) embedSrtToVideo(ctx context.Context, stepParam *types.SrtEmbedTaskStepParam, srtOptions dto.SrtOptions) error {
	// Update progress
	s.updateTaskStatus(stepParam.TaskId, types.TaskStatusProcessing, 20, "Converting SRT to ASS format", "", "")

	// Convert SRT to ASS format
	assPath := filepath.Join(stepParam.TaskBasePath, types.SrtEmbedTaskAssFileName)
	if err := s.convertSrtToAss(stepParam.SrtFilePath, assPath, srtOptions); err != nil {
		return fmt.Errorf("failed to convert SRT to ASS: %v", err)
	}

	// Log ASS file content for debugging
	if assContent, err := os.ReadFile(assPath); err == nil {
		contentLength := len(assContent)
		if contentLength > 1000 {
			contentLength = 1000
		}
		log.GetLogger().Info("Generated ASS file content",
			zap.String("taskId", stepParam.TaskId),
			zap.String("assPath", assPath),
			zap.String("content", string(assContent)[:contentLength]))
	}

	// Update progress
	s.updateTaskStatus(stepParam.TaskId, types.TaskStatusProcessing, 50, "Embedding subtitles into video", "", "")

	// Embed subtitles using ffmpeg
	if err := s.embedAssToVideo(stepParam.VideoFilePath, assPath, stepParam.OutputPath, srtOptions); err != nil {
		return fmt.Errorf("failed to embed subtitles: %v", err)
	}

	// Update progress
	s.updateTaskStatus(stepParam.TaskId, types.TaskStatusProcessing, 80, "Finalizing video processing", "", "")

	return nil
}

func (s Service) convertSrtToAss(srtPath, assPath string, srtOptions dto.SrtOptions) error {
	// Read SRT file
	srtFile, err := os.Open(srtPath)
	if err != nil {
		return fmt.Errorf("failed to open SRT file: %v", err)
	}
	defer srtFile.Close()

	// Create ASS file
	assFile, err := os.Create(assPath)
	if err != nil {
		return fmt.Errorf("failed to create ASS file: %v", err)
	}
	defer assFile.Close()

	// Write ASS header
	if err := s.writeAssHeader(assFile, srtOptions); err != nil {
		return fmt.Errorf("failed to write ASS header: %v", err)
	}

	// Parse SRT and convert to ASS
	scanner := bufio.NewScanner(srtFile)
	var currentBlock []string

	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())

		if line == "" {
			if len(currentBlock) > 0 {
				if err := s.processSrtBlock(currentBlock, assFile, srtOptions); err != nil {
					return fmt.Errorf("failed to process SRT block: %v", err)
				}
				currentBlock = nil
			}
		} else {
			currentBlock = append(currentBlock, line)
		}
	}

	// Process last block if exists
	if len(currentBlock) > 0 {
		if err := s.processSrtBlock(currentBlock, assFile, srtOptions); err != nil {
			return fmt.Errorf("failed to process last SRT block: %v", err)
		}
	}

	return scanner.Err()
}

func (s Service) writeAssHeader(assFile *os.File, srtOptions dto.SrtOptions) error {
	header := `[Script Info]
Title: Embedded Subtitles
ScriptType: v4.00+

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
Style: Default,%s,%d,&H%s,&H%s,&H%s,&H%s,0,0,0,0,100,100,0,0,1,%d,2,%d,10,10,10,1

[Events]
Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text
`

	// Convert colors from CSS format to ASS format (ASS uses BGR format with alpha)
	primaryColor, primaryAlpha := convertColorToAssWithAlpha(srtOptions.SubtitleStyle.FontColor)
	backgroundColor, backgroundAlpha := convertColorToAssWithAlpha(srtOptions.SubtitleStyle.BackgroundColor)
	outlineColor, _ := convertColorToAssWithAlpha(srtOptions.SubtitleStyle.OutlineColor)

	// Apply alpha to colors (ASS format: &HAABBGGRR where AA is alpha)
	primaryColorWithAlpha := fmt.Sprintf("%02X%s", 255-primaryAlpha, primaryColor)
	backgroundColorWithAlpha := fmt.Sprintf("%02X%s", 255-backgroundAlpha, backgroundColor)
	outlineColorWithAlpha := fmt.Sprintf("00%s", outlineColor) // No alpha for outline

	// Determine alignment based on position
	alignment := 2 // bottom center
	if srtOptions.SubtitleStyle.Position == "top" {
		alignment = 8 // top center
	}

	// Outline width - make it more visible
	outlineWidth := 0
	if srtOptions.SubtitleStyle.Outline {
		outlineWidth = 3 // Increased from 2 to 3 for better visibility
	}

	log.GetLogger().Info("ASS Style settings",
		zap.String("fontFamily", srtOptions.SubtitleStyle.FontFamily),
		zap.Int("fontSize", srtOptions.SubtitleStyle.FontSize),
		zap.String("primaryColorWithAlpha", primaryColorWithAlpha),
		zap.String("backgroundColorWithAlpha", backgroundColorWithAlpha),
		zap.String("outlineColorWithAlpha", outlineColorWithAlpha),
		zap.Int("outlineWidth", outlineWidth),
		zap.Int("alignment", alignment))

	formattedHeader := fmt.Sprintf(header,
		srtOptions.SubtitleStyle.FontFamily,
		srtOptions.SubtitleStyle.FontSize,
		primaryColorWithAlpha,
		primaryColorWithAlpha, // Secondary color same as primary
		outlineColorWithAlpha,
		backgroundColorWithAlpha,
		outlineWidth,
		alignment,
	)

	_, err := assFile.WriteString(formattedHeader)
	return err
}

func (s Service) processSrtBlock(block []string, assFile *os.File, srtOptions dto.SrtOptions) error {
	if len(block) < 3 {
		return nil // Invalid block
	}

	// Parse timestamp (line 1 is sequence number, line 2 is timestamp)
	timePattern := regexp.MustCompile(`(\d{2}:\d{2}:\d{2},\d{3}) --> (\d{2}:\d{2}:\d{2},\d{3})`)
	matches := timePattern.FindStringSubmatch(block[1])
	if len(matches) != 3 {
		return nil // Invalid timestamp
	}

	startTime := convertSrtTimeToAss(matches[1])
	endTime := convertSrtTimeToAss(matches[2])

	// Combine subtitle text (lines 2 onwards)
	text := strings.Join(block[2:], "\\N")
	text = strings.ReplaceAll(text, "\n", "\\N")

	// Write ASS dialogue line
	dialogue := fmt.Sprintf("Dialogue: 0,%s,%s,Default,,0,0,0,,%s\n", startTime, endTime, text)
	_, err := assFile.WriteString(dialogue)
	return err
}

func (s Service) embedAssToVideo(videoPath, assPath, outputPath string, srtOptions dto.SrtOptions) error {
	// Escape the ASS file path for FFmpeg
	escapedAssPath := strings.ReplaceAll(assPath, "\\", "/")
	escapedAssPath = strings.ReplaceAll(escapedAssPath, ":", "\\:")

	// Prepare ffmpeg command with better subtitle rendering
	cmd := exec.Command(storage.FfmpegPath,
		"-y",            // Overwrite output file
		"-i", videoPath, // Input video
		"-vf", fmt.Sprintf("ass='%s'", escapedAssPath), // Video filter with ASS subtitles (quoted path)
		"-c:v", "libx264", // Video codec
		"-preset", "medium", // Better quality preset
		"-crf", "23", // Constant rate factor for good quality
		"-c:a", "aac", // Audio codec
		"-b:a", "192k", // Audio bitrate
		"-movflags", "+faststart", // Optimize for web streaming
		outputPath, // Output file
	)

	log.GetLogger().Info("Executing FFmpeg command",
		zap.String("videoPath", videoPath),
		zap.String("assPath", assPath),
		zap.String("outputPath", outputPath),
		zap.String("command", cmd.String()))

	// Execute command
	output, err := cmd.CombinedOutput()
	if err != nil {
		log.GetLogger().Error("ffmpeg command failed",
			zap.String("command", cmd.String()),
			zap.String("output", string(output)),
			zap.Error(err))
		return fmt.Errorf("ffmpeg failed: %v, output: %s", err, string(output))
	}

	log.GetLogger().Info("Successfully embedded subtitles",
		zap.String("output", outputPath),
		zap.String("ffmpeg_output", string(output)))
	return nil
}

func convertColorToAssWithAlpha(cssColor string) (string, int) {
	// Convert CSS color to ASS BGR format and return alpha value
	cssColor = strings.TrimSpace(cssColor)

	if strings.HasPrefix(cssColor, "#") {
		// Remove # and convert hex to BGR
		hex := strings.TrimPrefix(cssColor, "#")
		if len(hex) == 6 {
			r := hex[0:2]
			g := hex[2:4]
			b := hex[4:6]
			return b + g + r, 255 // BGR format for ASS, full opacity
		} else if len(hex) == 3 {
			// Convert 3-digit hex to 6-digit
			r := string(hex[0]) + string(hex[0])
			g := string(hex[1]) + string(hex[1])
			b := string(hex[2]) + string(hex[2])
			return b + g + r, 255 // BGR format for ASS, full opacity
		}
	}

	// Handle rgba format - extract RGB and alpha values
	if strings.HasPrefix(cssColor, "rgba(") {
		// Extract rgba values: rgba(r,g,b,a)
		content := strings.TrimPrefix(cssColor, "rgba(")
		content = strings.TrimSuffix(content, ")")
		parts := strings.Split(content, ",")
		if len(parts) >= 4 {
			r, _ := strconv.Atoi(strings.TrimSpace(parts[0]))
			g, _ := strconv.Atoi(strings.TrimSpace(parts[1]))
			b, _ := strconv.Atoi(strings.TrimSpace(parts[2]))
			a, _ := strconv.ParseFloat(strings.TrimSpace(parts[3]), 64)
			alpha := int(a * 255) // Convert 0-1 to 0-255
			return fmt.Sprintf("%02X%02X%02X", b, g, r), alpha // BGR format
		}
	}

	// Handle rgb format
	if strings.HasPrefix(cssColor, "rgb(") {
		content := strings.TrimPrefix(cssColor, "rgb(")
		content = strings.TrimSuffix(content, ")")
		parts := strings.Split(content, ",")
		if len(parts) >= 3 {
			r, _ := strconv.Atoi(strings.TrimSpace(parts[0]))
			g, _ := strconv.Atoi(strings.TrimSpace(parts[1]))
			b, _ := strconv.Atoi(strings.TrimSpace(parts[2]))
			return fmt.Sprintf("%02X%02X%02X", b, g, r), 255 // BGR format, full opacity
		}
	}

	// Default color names
	switch strings.ToLower(cssColor) {
	case "white":
		return "FFFFFF", 255
	case "black":
		return "000000", 255
	case "red":
		return "0000FF", 255 // BGR: Blue=00, Green=00, Red=FF
	case "green":
		return "00FF00", 255 // BGR: Blue=00, Green=FF, Red=00
	case "blue":
		return "FF0000", 255 // BGR: Blue=FF, Green=00, Red=00
	case "yellow":
		return "00FFFF", 255 // BGR: Blue=00, Green=FF, Red=FF
	case "cyan":
		return "FFFF00", 255 // BGR: Blue=FF, Green=FF, Red=00
	case "magenta":
		return "FF00FF", 255 // BGR: Blue=FF, Green=00, Red=FF
	default:
		return "FFFFFF", 255 // Default to white, full opacity
	}
}

func convertColorToAss(cssColor string) string {
	// Convert CSS color to ASS BGR format
	cssColor = strings.TrimSpace(cssColor)

	if strings.HasPrefix(cssColor, "#") {
		// Remove # and convert hex to BGR
		hex := strings.TrimPrefix(cssColor, "#")
		if len(hex) == 6 {
			r := hex[0:2]
			g := hex[2:4]
			b := hex[4:6]
			return b + g + r // BGR format for ASS
		} else if len(hex) == 3 {
			// Convert 3-digit hex to 6-digit
			r := string(hex[0]) + string(hex[0])
			g := string(hex[1]) + string(hex[1])
			b := string(hex[2]) + string(hex[2])
			return b + g + r // BGR format for ASS
		}
	}

	// Handle rgba format - extract RGB values
	if strings.HasPrefix(cssColor, "rgba(") {
		// Extract rgba values: rgba(r,g,b,a)
		content := strings.TrimPrefix(cssColor, "rgba(")
		content = strings.TrimSuffix(content, ")")
		parts := strings.Split(content, ",")
		if len(parts) >= 3 {
			r, _ := strconv.Atoi(strings.TrimSpace(parts[0]))
			g, _ := strconv.Atoi(strings.TrimSpace(parts[1]))
			b, _ := strconv.Atoi(strings.TrimSpace(parts[2]))
			return fmt.Sprintf("%02X%02X%02X", b, g, r) // BGR format
		}
	}

	// Handle rgb format
	if strings.HasPrefix(cssColor, "rgb(") {
		content := strings.TrimPrefix(cssColor, "rgb(")
		content = strings.TrimSuffix(content, ")")
		parts := strings.Split(content, ",")
		if len(parts) >= 3 {
			r, _ := strconv.Atoi(strings.TrimSpace(parts[0]))
			g, _ := strconv.Atoi(strings.TrimSpace(parts[1]))
			b, _ := strconv.Atoi(strings.TrimSpace(parts[2]))
			return fmt.Sprintf("%02X%02X%02X", b, g, r) // BGR format
		}
	}

	// Default color names
	switch strings.ToLower(cssColor) {
	case "white":
		return "FFFFFF"
	case "black":
		return "000000"
	case "red":
		return "0000FF" // BGR: Blue=00, Green=00, Red=FF
	case "green":
		return "00FF00" // BGR: Blue=00, Green=FF, Red=00
	case "blue":
		return "FF0000" // BGR: Blue=FF, Green=00, Red=00
	case "yellow":
		return "00FFFF" // BGR: Blue=00, Green=FF, Red=FF
	case "cyan":
		return "FFFF00" // BGR: Blue=FF, Green=FF, Red=00
	case "magenta":
		return "FF00FF" // BGR: Blue=FF, Green=00, Red=FF
	default:
		return "FFFFFF" // Default to white
	}
}

func convertSrtTimeToAss(srtTime string) string {
	// Convert SRT time format (HH:MM:SS,mmm) to ASS format (H:MM:SS.cc)
	parts := strings.Split(srtTime, ",")
	if len(parts) != 2 {
		return srtTime
	}

	timePart := parts[0]
	milliseconds := parts[1]

	// Convert milliseconds to centiseconds
	if ms, err := strconv.Atoi(milliseconds); err == nil {
		centiseconds := ms / 10
		return fmt.Sprintf("%s.%02d", timePart, centiseconds)
	}

	return srtTime
}
