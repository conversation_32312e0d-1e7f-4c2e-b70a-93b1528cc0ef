package types

import "time"

const (
	TaskStatusCreated    = "created"
	TaskStatusProcessing = "processing"
	TaskStatusCompleted  = "completed"
	TaskStatusFailed     = "failed"
)

type SrtEmbedTask struct {
	TaskId         string    `json:"task_id"`
	Status         string    `json:"status"`
	ProcessPercent int       `json:"process_percent"`
	Message        string    `json:"message"`
	VideoFilePath  string    `json:"video_file_path"`
	SrtFilePath    string    `json:"srt_file_path"`
	SrtOptions     string    `json:"srt_options"` // JSON string of SrtOptions
	OutputPath     string    `json:"output_path"`
	VideoUrl       string    `json:"video_url"`
	Error          string    `json:"error"`
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
}

type SrtEmbedTaskStepParam struct {
	TaskId         string
	TaskPtr        *SrtEmbedTask
	TaskBasePath   string
	VideoFilePath  string
	SrtFilePath    string
	SrtOptionsJson string
	OutputPath     string
}

const (
	SrtEmbedTaskVideoFileName  = "input_video.mp4"
	SrtEmbedTaskSrtFileName    = "input_subtitles.srt"
	SrtEmbedTaskOutputFileName = "output_video.mp4"
	SrtEmbedTaskAssFileName    = "formatted_subtitles.ass"
)
