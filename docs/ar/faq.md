### 1. لا يمكن رؤية ملف التكوين `app.log`، ولا يمكن معرفة محتوى الخطأ
لمستخدمي Windows، يرجى وضع دليل عمل هذا البرنامج في مجلد غير موجود على القرص C.

### 2. تم إنشاء ملف التكوين في النسخة غير المكتبية، ولكن لا يزال يظهر الخطأ "لا يمكن العثور على ملف التكوين"
تأكد من أن اسم ملف التكوين هو `config.toml`، وليس `config.toml.txt` أو أي شيء آخر.
بعد الانتهاء من التكوين، يجب أن تكون بنية مجلد العمل الخاص بالبرنامج كما يلي:
```
/── config/
│   └── config.toml
├── cookies.txt (ملف cookies.txt اختياري)
└── krillinai.exe
```

### 3. تم ملء تكوين النموذج الكبير، ولكن يظهر الخطأ "xxxxx يحتاج إلى تكوين xxxxx API Key"
على الرغم من أن خدمة النموذج وخدمة الصوت يمكن أن تستخدم كلاهما خدمات openai، إلا أن هناك أيضًا سيناريوهات حيث يستخدم النموذج الكبير خدمات غير openai بشكل منفصل، لذا فإن هذين التكوينين منفصلان. بالإضافة إلى تكوين النموذج الكبير، يرجى البحث عن تكوين whisper أدناه لملء المفتاح والمعلومات ذات الصلة.

### 4. يظهر الخطأ "yt-dlp error"
مشكلة في برنامج تنزيل الفيديو، يبدو أنها تتعلق بمشكلة في الشبكة أو إصدار برنامج التنزيل. تحقق مما إذا كان وكيل الشبكة مفعلًا ومكونًا في عنصر تكوين الوكيل في ملف التكوين، ومن المستحسن اختيار نقطة اتصال في هونغ كونغ. يتم تثبيت برنامج التنزيل تلقائيًا بواسطة هذا البرنامج، سأقوم بتحديث مصدر التثبيت ولكن نظرًا لأنه ليس مصدرًا رسميًا، فقد يكون هناك تأخير. إذا واجهت مشكلة، حاول تحديثه يدويًا، وطريقة التحديث هي:

افتح الطرفية في موقع دليل bin للبرنامج، ثم نفذ
```
./yt-dlp.exe -U
```
استبدل `yt-dlp.exe` باسم برنامج ytdlp الفعلي في نظامك.

### 5. بعد النشر، يتم إنشاء الترجمة بشكل طبيعي، ولكن الترجمة المدمجة في الفيديو تحتوي على الكثير من الرموز غير المفهومة
غالبًا ما يكون السبب هو نقص خطوط اللغة الصينية في Linux. يرجى تنزيل خط [微软雅黑](https://modelscope.cn/models/Maranello/KrillinAI_dependency_cn/resolve/master/%E5%AD%97%E4%BD%93/msyh.ttc) وخط [微软雅黑-bold](https://modelscope.cn/models/Maranello/KrillinAI_dependency_cn/resolve/master/%E5%AD%97%E4%BD%93/msyhbd.ttc) (أو اختيار الخط الذي يلبي متطلباتك)، ثم اتبع الخطوات التالية:
1. أنشئ مجلد msyh في /usr/share/fonts/ وانسخ الخطوط التي تم تنزيلها إلى هذا الدليل
2. 
    ```
    cd /usr/share/fonts/msyh
    sudo mkfontscale
    sudo mkfontdir
    fc-cache
    ```