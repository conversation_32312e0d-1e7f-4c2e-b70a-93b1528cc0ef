# Subtitle Styling Troubleshooting Guide

## Overview

This guide helps troubleshoot subtitle color and size issues in the video processing API.

## Recent Fixes Applied

### 1. Color Format Conversion
- **Fixed**: Proper CSS to ASS BGR color conversion
- **Added**: Support for `rgba()`, `rgb()`, and hex colors
- **Added**: Alpha transparency handling

### 2. ASS Format Improvements
- **Fixed**: ASS style header with proper color fields
- **Added**: Alpha channel support (`&HAABBGGRR` format)
- **Increased**: Outline width for better visibility

### 3. FFmpeg Optimization
- **Fixed**: Proper path escaping for ASS files
- **Added**: Better video quality settings
- **Improved**: Command logging for debugging

## Color Format Examples

### Supported Color Formats

```json
{
  "subtitleStyle": {
    "fontColor": "white",           // Named colors
    "fontColor": "#FFFFFF",         // Hex colors
    "fontColor": "#FFF",            // Short hex
    "fontColor": "rgb(255,255,255)", // RGB format
    "backgroundColor": "rgba(0,0,0,0.8)" // RGBA with transparency
  }
}
```

### Color Conversion Examples

| CSS Color | ASS BGR Format | Description |
|-----------|----------------|-------------|
| `white` | `FFFFFF` | White text |
| `black` | `000000` | Black text |
| `red` | `0000FF` | Red text (BGR format) |
| `yellow` | `00FFFF` | Yellow text |
| `rgba(0,0,0,0.8)` | `CC000000` | Semi-transparent black |

## Font Size Guidelines

- **Small**: 16-20px for mobile/small screens
- **Medium**: 20-24px for standard videos
- **Large**: 24-32px for high-resolution videos
- **Extra Large**: 32px+ for accessibility

## Debugging Steps

### 1. Check Logs
Look for these log entries:
```
ASS Style settings - Shows converted colors and settings
Generated ASS file content - Shows the ASS file structure
Executing FFmpeg command - Shows the FFmpeg command used
```

### 2. Verify ASS File
The generated ASS file should contain:
```
[V4+ Styles]
Style: Default,Arial,24,&H00FFFFFF,&H00FFFFFF,&H00000000,&HCC000000,0,0,0,0,100,100,0,0,1,3,2,2,10,10,10,1
```

### 3. Test Different Styles

#### High Contrast (Recommended)
```json
{
  "fontFamily": "Arial",
  "fontSize": 24,
  "fontColor": "white",
  "backgroundColor": "rgba(0,0,0,0.9)",
  "position": "bottom",
  "outline": true,
  "outlineColor": "black"
}
```

#### Colored Subtitles
```json
{
  "fontFamily": "Arial",
  "fontSize": 28,
  "fontColor": "yellow",
  "backgroundColor": "rgba(0,0,0,0.8)",
  "position": "bottom",
  "outline": true,
  "outlineColor": "red"
}
```

## Common Issues and Solutions

### Issue: Colors Not Showing
**Solution**: Check color format conversion in logs
- Ensure colors are in supported format
- Check ASS file generation logs

### Issue: Text Too Small/Large
**Solution**: Adjust fontSize parameter
- Test with different sizes (16-32px)
- Consider video resolution

### Issue: Poor Contrast
**Solution**: Improve styling
- Use high contrast colors
- Increase outline width
- Adjust background transparency

### Issue: Subtitles Not Visible
**Solution**: Check positioning and colors
- Verify position setting (top/bottom)
- Ensure font color differs from background
- Check outline settings

## Testing Commands

### Basic Test
```bash
curl --location 'http://localhost:8888/api/capability/task' \
  --form 'videoFile=@"video.mp4"' \
  --form 'srtFile=@"subtitles.srt"' \
  --form 'srtOptions="{\"embedOriginalSubtitles\":true,\"subtitleStyle\":{\"fontFamily\":\"Arial\",\"fontSize\":24,\"fontColor\":\"white\",\"backgroundColor\":\"rgba(0,0,0,0.8)\",\"position\":\"bottom\",\"outline\":true,\"outlineColor\":\"black\"},\"outputFormat\":\"mp4\"}"'
```

### Colored Test
```bash
curl --location 'http://localhost:8888/api/capability/task' \
  --form 'videoFile=@"video.mp4"' \
  --form 'srtFile=@"subtitles.srt"' \
  --form 'srtOptions="{\"embedOriginalSubtitles\":true,\"subtitleStyle\":{\"fontFamily\":\"Arial\",\"fontSize\":28,\"fontColor\":\"yellow\",\"backgroundColor\":\"rgba(0,0,0,0.9)\",\"position\":\"bottom\",\"outline\":true,\"outlineColor\":\"red\"},\"outputFormat\":\"mp4\"}"'
```

## Log Analysis

Check application logs for:
1. **Color conversion**: Verify CSS colors are properly converted
2. **ASS generation**: Ensure ASS file is created correctly
3. **FFmpeg execution**: Check for command errors
4. **File paths**: Verify all file paths are correct

## Support

If issues persist:
1. Check the generated ASS file manually
2. Test FFmpeg command separately
3. Verify video codec compatibility
4. Check font availability on system
