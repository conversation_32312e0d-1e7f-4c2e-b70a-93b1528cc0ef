package handler

import (
	"encoding/json"
	"path/filepath"
	"strings"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"krillin-ai/internal/dto"
	"krillin-ai/internal/response"
	"krillin-ai/log"
)

func (h <PERSON>ler) CreateTask(c *gin.Context) {
	// Parse multipart form
	form, err := c.MultipartForm()
	if err != nil {
		log.GetLogger().Error("CreateTask failed to parse multipart form", zap.Error(err))
		response.R(c, response.Response{
			Error: -1,
			Msg:   "Failed to parse form data",
			Data:  nil,
		})
		return
	}

	// Get video file
	videoFiles := form.File["videoFile"]
	if len(videoFiles) == 0 {
		response.R(c, response.Response{
			Error: -1,
			Msg:   "Video file is required",
			Data:  nil,
		})
		return
	}
	videoFile := videoFiles[0]

	// Get SRT file
	srtFiles := form.File["srtFile"]
	if len(srtFiles) == 0 {
		response.R(c, response.Response{
			Error: -1,
			Msg:   "SRT file is required",
			Data:  nil,
		})
		return
	}
	srtFile := srtFiles[0]

	// Get SRT options
	srtOptionsStr := c.PostForm("srtOptions")
	if srtOptionsStr == "" {
		response.R(c, response.Response{
			Error: -1,
			Msg:   "SRT options are required",
			Data:  nil,
		})
		return
	}

	var srtOptions dto.SrtOptions
	if err := json.Unmarshal([]byte(srtOptionsStr), &srtOptions); err != nil {
		log.GetLogger().Error("CreateTask failed to parse SRT options", zap.Error(err))
		response.R(c, response.Response{
			Error: -1,
			Msg:   "Invalid SRT options format",
			Data:  nil,
		})
		return
	}

	// Validate file types
	if !isValidVideoFile(videoFile.Filename) {
		response.R(c, response.Response{
			Error: -1,
			Msg:   "Invalid video file format. Supported formats: mp4, avi, mov, mkv",
			Data:  nil,
		})
		return
	}

	if !isValidSrtFile(srtFile.Filename) {
		response.R(c, response.Response{
			Error: -1,
			Msg:   "Invalid SRT file format. Only .srt files are supported",
			Data:  nil,
		})
		return
	}

	// Create task
	taskRes, err := h.Service.CreateSrtEmbedTask(videoFile, srtFile, srtOptions)
	if err != nil {
		log.GetLogger().Error("CreateTask failed to create task", zap.Error(err))
		response.R(c, response.Response{
			Error: -1,
			Msg:   err.Error(),
			Data:  nil,
		})
		return
	}

	response.R(c, response.Response{
		Error: 0,
		Msg:   "Task created successfully",
		Data:  taskRes,
	})
}

func (h Handler) GetTaskStatus(c *gin.Context) {
	taskId := strings.Trim(c.Param("taskid"), "/")
	if taskId == "" {
		response.R(c, response.Response{
			Error: -1,
			Msg:   "Task ID is required",
			Data:  nil,
		})
		return
	}

	status, err := h.Service.GetSrtEmbedTaskStatus(taskId)
	if err != nil {
		log.GetLogger().Error("GetTaskStatus failed", zap.String("taskId", taskId), zap.Error(err))
		response.R(c, response.Response{
			Error: -1,
			Msg:   err.Error(),
			Data:  nil,
		})
		return
	}

	response.R(c, response.Response{
		Error: 0,
		Msg:   "Success",
		Data:  status,
	})
}

func isValidVideoFile(filename string) bool {
	ext := strings.ToLower(filepath.Ext(filename))
	validExts := []string{".mp4", ".avi", ".mov", ".mkv", ".webm", ".flv"}
	for _, validExt := range validExts {
		if ext == validExt {
			return true
		}
	}
	return false
}

func isValidSrtFile(filename string) bool {
	ext := strings.ToLower(filepath.Ext(filename))
	return ext == ".srt"
}
