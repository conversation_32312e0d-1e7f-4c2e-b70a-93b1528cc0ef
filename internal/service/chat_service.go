package service

import (
	"context"
	"fmt"
	"io"
	"strings"

	"github.com/sashabaranov/go-openai"
	"go.uber.org/zap"
	"krillin-ai/config"
	"krillin-ai/internal/dto"
	"krillin-ai/log"
	"krillin-ai/pkg/aliyun"
	openaiPkg "krillin-ai/pkg/openai"
)

type ChatService struct {
	openaiClient    *openaiPkg.Client
	aliyunClient    *aliyun.ChatClient
	anthropicClient *AnthropicClient
}

func NewChatService() *ChatService {
	// Initialize OpenAI client
	openaiClient := openaiPkg.NewClient(config.Conf.Llm.BaseUrl, config.Conf.Llm.ApiKey, config.Conf.App.Proxy)

	// Initialize Aliyun client (if configured)
	var aliyunClient *aliyun.ChatClient
	if config.Conf.Llm.ApiKey != "" {
		aliyunClient = aliyun.NewChatClient(config.Conf.Llm.Api<PERSON>ey)
	}

	// Initialize Anthropic client (if configured)
	var anthropicClient *AnthropicClient
	if config.Conf.Llm.ApiKey != "" {
		anthropicClient = NewAnthropicClient(config.Conf.Llm.ApiKey, config.Conf.App.Proxy)
	}

	return &ChatService{
		openaiClient:    openaiClient,
		aliyunClient:    aliyunClient,
		anthropicClient: anthropicClient,
	}
}

func (cs *ChatService) ChatCompletion(req dto.ChatRequest) (*dto.ChatResponse, error) {
	log.GetLogger().Info("Processing chat completion request",
		zap.String("provider", req.Provider),
		zap.String("model", req.Model),
		zap.Int("prompt_length", len(req.Prompt)))

	switch strings.ToLower(req.Provider) {
	case "openai":
		return cs.openaiChatCompletion(req)
	case "aliyun", "qwen":
		return cs.aliyunChatCompletion(req)
	case "anthropic", "claude":
		return cs.anthropicChatCompletion(req)
	default:
		return nil, fmt.Errorf("unsupported provider: %s", req.Provider)
	}
}

func (cs *ChatService) openaiChatCompletion(req dto.ChatRequest) (*dto.ChatResponse, error) {
	if cs.openaiClient == nil {
		return nil, fmt.Errorf("OpenAI client not configured")
	}

	// Use model from request or default
	model := req.Model
	if model == "" {
		model = config.Conf.Llm.Model
		if model == "" {
			model = "gpt-4o-mini"
		}
	}

	// Create OpenAI client for direct API call
	cfg := openai.DefaultConfig(config.Conf.Llm.ApiKey)
	if config.Conf.Llm.BaseUrl != "" {
		cfg.BaseURL = config.Conf.Llm.BaseUrl
	}
	client := openai.NewClientWithConfig(cfg)

	chatReq := openai.ChatCompletionRequest{
		Model: model,
		Messages: []openai.ChatCompletionMessage{
			{
				Role:    openai.ChatMessageRoleUser,
				Content: req.Prompt,
			},
		},
		MaxTokens: 4096,
	}

	resp, err := client.CreateChatCompletion(context.Background(), chatReq)
	if err != nil {
		log.GetLogger().Error("OpenAI chat completion failed", zap.Error(err))
		return nil, fmt.Errorf("OpenAI API error: %v", err)
	}

	if len(resp.Choices) == 0 {
		return nil, fmt.Errorf("no response from OpenAI")
	}

	usage := &dto.Usage{
		PromptTokens:     resp.Usage.PromptTokens,
		CompletionTokens: resp.Usage.CompletionTokens,
		TotalTokens:      resp.Usage.TotalTokens,
	}

	return &dto.ChatResponse{
		Response: resp.Choices[0].Message.Content,
		Provider: "openai",
		Model:    model,
		Usage:    usage,
	}, nil
}

func (cs *ChatService) aliyunChatCompletion(req dto.ChatRequest) (*dto.ChatResponse, error) {
	if cs.aliyunClient == nil {
		return nil, fmt.Errorf("Aliyun client not configured")
	}

	model := req.Model
	if model == "" {
		model = "qwen-plus"
	}

	// Create Aliyun client for direct API call
	cfg := openai.DefaultConfig(config.Conf.Llm.ApiKey)
	cfg.BaseURL = "https://dashscope.aliyuncs.com/compatible-mode/v1"
	client := openai.NewClientWithConfig(cfg)

	chatReq := openai.ChatCompletionRequest{
		Model: model,
		Messages: []openai.ChatCompletionMessage{
			{
				Role:    openai.ChatMessageRoleUser,
				Content: req.Prompt,
			},
		},
		MaxTokens: 4096,
	}

	resp, err := client.CreateChatCompletion(context.Background(), chatReq)
	if err != nil {
		log.GetLogger().Error("Aliyun chat completion failed", zap.Error(err))
		return nil, fmt.Errorf("Aliyun API error: %v", err)
	}

	if len(resp.Choices) == 0 {
		return nil, fmt.Errorf("no response from Aliyun")
	}

	usage := &dto.Usage{
		PromptTokens:     resp.Usage.PromptTokens,
		CompletionTokens: resp.Usage.CompletionTokens,
		TotalTokens:      resp.Usage.TotalTokens,
	}

	return &dto.ChatResponse{
		Response: resp.Choices[0].Message.Content,
		Provider: "aliyun",
		Model:    model,
		Usage:    usage,
	}, nil
}

func (cs *ChatService) anthropicChatCompletion(req dto.ChatRequest) (*dto.ChatResponse, error) {
	if cs.anthropicClient == nil {
		return nil, fmt.Errorf("Anthropic client not configured")
	}

	model := req.Model
	if model == "" {
		model = "claude-3-haiku-20240307"
	}

	response, err := cs.anthropicClient.CreateMessage(req.Prompt, model)
	if err != nil {
		log.GetLogger().Error("Anthropic chat completion failed", zap.Error(err))
		return nil, fmt.Errorf("Anthropic API error: %v", err)
	}

	return &dto.ChatResponse{
		Response: response,
		Provider: "anthropic",
		Model:    model,
	}, nil
}

func (cs *ChatService) StreamChatCompletion(req dto.ChatRequest) (<-chan dto.StreamChatResponse, error) {
	responseChan := make(chan dto.StreamChatResponse, 100)

	go func() {
		defer close(responseChan)

		switch strings.ToLower(req.Provider) {
		case "openai":
			cs.streamOpenAI(req, responseChan)
		case "aliyun", "qwen":
			cs.streamAliyun(req, responseChan)
		default:
			responseChan <- dto.StreamChatResponse{
				Delta:    fmt.Sprintf("Streaming not supported for provider: %s", req.Provider),
				Provider: req.Provider,
				Done:     true,
			}
		}
	}()

	return responseChan, nil
}

func (cs *ChatService) streamOpenAI(req dto.ChatRequest, responseChan chan<- dto.StreamChatResponse) {
	model := req.Model
	if model == "" {
		model = config.Conf.Llm.Model
		if model == "" {
			model = "gpt-4o-mini"
		}
	}

	cfg := openai.DefaultConfig(config.Conf.Llm.ApiKey)
	if config.Conf.Llm.BaseUrl != "" {
		cfg.BaseURL = config.Conf.Llm.BaseUrl
	}
	client := openai.NewClientWithConfig(cfg)

	chatReq := openai.ChatCompletionRequest{
		Model: model,
		Messages: []openai.ChatCompletionMessage{
			{
				Role:    openai.ChatMessageRoleUser,
				Content: req.Prompt,
			},
		},
		Stream:    true,
		MaxTokens: 4096,
	}

	stream, err := client.CreateChatCompletionStream(context.Background(), chatReq)
	if err != nil {
		responseChan <- dto.StreamChatResponse{
			Delta:    fmt.Sprintf("Error: %v", err),
			Provider: "openai",
			Model:    model,
			Done:     true,
		}
		return
	}
	defer stream.Close()

	for {
		response, err := stream.Recv()
		if err == io.EOF {
			responseChan <- dto.StreamChatResponse{
				Delta:    "",
				Provider: "openai",
				Model:    model,
				Done:     true,
			}
			break
		}
		if err != nil {
			responseChan <- dto.StreamChatResponse{
				Delta:    fmt.Sprintf("Stream error: %v", err),
				Provider: "openai",
				Model:    model,
				Done:     true,
			}
			break
		}

		if len(response.Choices) > 0 {
			responseChan <- dto.StreamChatResponse{
				Delta:    response.Choices[0].Delta.Content,
				Provider: "openai",
				Model:    model,
				Done:     false,
			}
		}
	}
}

func (cs *ChatService) streamAliyun(req dto.ChatRequest, responseChan chan<- dto.StreamChatResponse) {
	// Similar implementation for Aliyun streaming
	// For now, fall back to non-streaming
	resp, err := cs.aliyunChatCompletion(req)
	if err != nil {
		responseChan <- dto.StreamChatResponse{
			Delta:    fmt.Sprintf("Error: %v", err),
			Provider: "aliyun",
			Done:     true,
		}
		return
	}

	responseChan <- dto.StreamChatResponse{
		Delta:    resp.Response,
		Provider: "aliyun",
		Model:    resp.Model,
		Done:     true,
	}
}

func (cs *ChatService) GetAvailableProviders() *dto.ProvidersListResponse {
	providers := []dto.ProviderConfig{
		{
			Name:    "openai",
			BaseURL: config.Conf.Llm.BaseUrl,
			Model:   config.Conf.Llm.Model,
			Enabled: config.Conf.Llm.ApiKey != "",
		},
		{
			Name:    "aliyun",
			BaseURL: "https://dashscope.aliyuncs.com/compatible-mode/v1",
			Model:   "qwen-plus",
			Enabled: config.Conf.Llm.ApiKey != "",
		},
		{
			Name:    "anthropic",
			BaseURL: "https://api.anthropic.com",
			Model:   "claude-3-haiku-20240307",
			Enabled: config.Conf.Llm.ApiKey != "",
		},
	}

	return &dto.ProvidersListResponse{
		Providers: providers,
	}
}
