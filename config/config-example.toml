[app]
    segment_duration = 5 # 音频切分处理间隔，单位：分钟，建议值：5-10，如果视频中话语较少可以适当提高
    transcribe_parallel_num = 1 # 并发进行转录的数量上限，建议值：1-3，如果使用了本地模型，最好调成1
    translate_parallel_num = 3 # 并发进行翻译的数量上限，建议值：3，倍于转录的并发量，如果使用TPM限制严格的API，可以适当调低
    transcribe_max_attempts = 3 # 转录最大尝试次数，建议值：3
    translate_max_attempts = 5 # 翻译最大尝试次数，建议值：5，如果模型参数量较少或翻译失败率较高可以适当调高
    proxy = "" # 网络代理地址，格式如http://127.0.0.1:7890，可不填

[server]
    host = "127.0.0.1"
    port = 8888

# 下方的配置不是都要填，请结合文档说明进行配置

[llm] #支持openai,deepseek,通义千问等所有兼容openai请求格式的模型服务
    base_url = "" # 自定义base url，可配合转发站密钥使用，留空为openai官方api
    api_key = "" # API密钥
    model = "" # 指定模型名，可通过此字段结合base_url使用外部任何与OpenAI API兼容的大模型服务，留空默认为gpt-4o-mini

[transcribe] # 视频转文本支持多种方案，配置时先填provider，再填对应的配置
    [transcribe.provider]
        name = "openai" #语音识别，当前可选值：openai,fasterwhisper,whisperkit,whisper.cpp,aliyun。(fasterwhisper不支持macOS,whisperkit只支持M芯片)
    [transcribe.openai]
        base_url = ""
        api_key = "********************************************************************************************************************************************************************"
        model = "whisper-1"
    [transcribe.fasterwhisper]
        model = "medium" # fasterwhisper的本地模型可选值：tiny,medium,large-v2。建议medium及以上
    [transcribe.whisperkit]
        model = "large-v2" # whisperkit的本地模型可选值：large-v2
    [transcribe.whispercpp]
        model = "large-v2" # whispercpp的本地模型可选值：large-v2
    [transcribe.aliyun] # provider选aliyun这块就都要填
        [tts.aliyun.oss]
            access_key_id = ""
            access_key_secret = ""
            bucket = ""
        [transcribe.aliyun.speech]
            access_key_id = ""
            access_key_secret = ""
            app_key= ""

[tts]
    [tts.provider]
        name = "aliyun" # 可选值：openai,aliyun
    [tts.openai]
        base_url = ""
        api_key = ""
        model = "" # gpt-4o-mini-tts, tts-1, tts-1-hd
    [tts.aliyun] # provider选aliyun这块就都要填
        [tts.aliyun.oss]
            access_key_id = ""
            access_key_secret = ""
            bucket = ""
        [tts.aliyun.speech]
            access_key_id = ""
            access_key_secret = ""
            app_key= ""

[cloudflare_r2]
    access_key_id = ""     # Cloudflare R2 Access Key ID
    secret_access_key = "" # Cloudflare R2 Secret Access Key
    bucket = ""            # R2 bucket name
    endpoint = ""          # R2 endpoint URL (e.g., https://your-account-id.r2.cloudflarestorage.com)
    region = "auto"        # R2 region (usually "auto")