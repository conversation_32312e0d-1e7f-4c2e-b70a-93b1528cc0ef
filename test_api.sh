#!/bin/bash

# Test script for the new task API endpoint
# This script tests the /api/capability/task endpoint

echo "Testing the new task API endpoint..."

# Create a simple test SRT file
cat > test_subtitles.srt << 'EOF'
1
00:00:01,000 --> 00:00:05,000
Hello, this is a test subtitle.

2
00:00:06,000 --> 00:00:10,000
This is the second subtitle line.

3
00:00:11,000 --> 00:00:15,000
And this is the third subtitle.
EOF

# Create SRT options JSON
SRT_OPTIONS='{
  "embedOriginalSubtitles": true,
  "embedTranslatedSubtitles": true,
  "subtitleStyle": {
    "fontFamily": "Arial",
    "fontSize": 18,
    "fontColor": "white",
    "backgroundColor": "rgba(0,0,0,0.8)",
    "position": "bottom",
    "outline": true,
    "outlineColor": "black"
  },
  "outputFormat": "mp4"
}'

echo "SRT Options: $SRT_OPTIONS"

# Test the API endpoint
echo "Making API request..."

# Note: You'll need to provide actual video and SRT files for a real test
# This is just showing the curl command structure

echo "curl --location 'http://localhost:8888/api/capability/task' \\"
echo "  --form 'videoFile=@\"path/to/your/video.mp4\"' \\"
echo "  --form 'srtFile=@\"test_subtitles.srt\"' \\"
echo "  --form 'srtOptions=\"$SRT_OPTIONS\"'"

echo ""
echo "To test with actual files, replace the paths above with real video and SRT files."
echo ""
echo "Example response should be:"
echo '{"error":0,"msg":"Task created successfully","data":{"task_id":"1234567890_abcdefgh"}}'
echo ""
echo "To check task status:"
echo "curl 'http://localhost:8888/api/capability/task?taskId=TASK_ID_HERE'"

# Clean up
rm -f test_subtitles.srt

echo "Test script completed."
