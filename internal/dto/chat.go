package dto

type ChatRequest struct {
	Provider string `json:"provider" binding:"required"` // openai, aliyun, anthropic, etc.
	Prompt   string `json:"prompt" binding:"required"`   // User prompt
	Model    string `json:"model,omitempty"`             // Optional model override
	Stream   bool   `json:"stream,omitempty"`            // Whether to stream response
}

type ChatResponse struct {
	Response string `json:"response"`
	Provider string `json:"provider"`
	Model    string `json:"model"`
	Usage    *Usage `json:"usage,omitempty"`
}

type Usage struct {
	PromptTokens     int `json:"prompt_tokens"`
	CompletionTokens int `json:"completion_tokens"`
	TotalTokens      int `json:"total_tokens"`
}

type StreamChatResponse struct {
	Delta    string `json:"delta"`
	Provider string `json:"provider"`
	Model    string `json:"model"`
	Done     bool   `json:"done"`
}

type ProviderConfig struct {
	Name     string `json:"name"`
	BaseURL  string `json:"base_url"`
	APIKey   string `json:"api_key"`
	Model    string `json:"model"`
	Enabled  bool   `json:"enabled"`
}

type ProvidersListResponse struct {
	Providers []ProviderConfig `json:"providers"`
}
