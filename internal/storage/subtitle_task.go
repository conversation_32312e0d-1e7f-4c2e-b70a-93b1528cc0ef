package storage

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
	"krillin-ai/config"
	"krillin-ai/internal/types"
)

var SubtitleTasks = NewRedisMap("sub") // sync.Map{} // task id -> SubtitleTask，用于接口查询数据

type RedisMap struct {
	client *redis.Client
	prefix string
}

// NewRedisMap creates a new RedisMap instance
func NewRedisMap(prefix string) *RedisMap {
	rdb := redis.NewClient(&redis.Options{
		Addr:     fmt.Sprintf("%s:%d", config.Conf.Redis.Host, config.Conf.Redis.Port),
		Password: config.Conf.Redis.Password,
		DB:       config.Conf.Redis.DB,
	})

	return &RedisMap{
		client: rdb,
		prefix: prefix,
	}
}

// Load retrieves a value from Redis, similar to sync.Map.Load
func (rm *RedisMap) Load(key string, dest interface{}) error {
	ctx := context.Background()
	fullKey := rm.getFullKey(key)

	val, err := rm.client.Get(ctx, fullKey).Result()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			return nil
		}
		return err
	}

	// Deserialize the JSON back to SubtitleTask
	if err := json.Unmarshal([]byte(val), dest); err != nil {
		return err
	}

	return nil
}

// Store saves a value to Redis, similar to sync.Map.Store
func (rm *RedisMap) Store(key string, value interface{}) {
	ctx := context.Background()
	fullKey := rm.getFullKey(key)

	// Serialize the value to JSON
	jsonData, err := json.Marshal(value)
	if err != nil {
		// Log error but don't panic to maintain sync.Map behavior
		return
	}

	// Store with a reasonable TTL (24 hours)
	rm.client.Set(ctx, fullKey, jsonData, 24*time.Hour)
}

// Delete removes a key from Redis, similar to sync.Map.Delete
func (rm *RedisMap) Delete(key string) {
	ctx := context.Background()
	fullKey := rm.getFullKey(key)
	rm.client.Del(ctx, fullKey)
}

// Range iterates over all keys with the prefix, similar to sync.Map.Range
func (rm *RedisMap) Range(f func(key, value interface{}) bool) {
	ctx := context.Background()
	pattern := rm.getFullKey("*")

	keys, err := rm.client.Keys(ctx, pattern).Result()
	if err != nil {
		return
	}

	for _, fullKey := range keys {
		val, err := rm.client.Get(ctx, fullKey).Result()
		if err != nil {
			continue
		}

		var task types.SubtitleTask
		if err := json.Unmarshal([]byte(val), &task); err != nil {
			continue
		}

		// Extract original key by removing prefix
		originalKey := rm.getOriginalKey(fullKey)
		if !f(originalKey, &task) {
			break
		}
	}
}

// getFullKey adds prefix to the key
func (rm *RedisMap) getFullKey(key string) string {
	return fmt.Sprintf("%s:%s", rm.prefix, key)
}

// getOriginalKey removes prefix from the full key
func (rm *RedisMap) getOriginalKey(fullKey string) string {
	prefixLen := len(rm.prefix) + 1 // +1 for the colon
	if len(fullKey) > prefixLen {
		return fullKey[prefixLen:]
	}
	return fullKey
}

// Close closes the Redis connection
func (rm *RedisMap) Close() error {
	return rm.client.Close()
}
