package service

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"time"

	"go.uber.org/zap"
	"krillin-ai/config"
	"krillin-ai/log"
)

type AnthropicClient struct {
	apiKey     string
	baseURL    string
	httpClient *http.Client
}

type AnthropicMessage struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

type AnthropicRequest struct {
	Model     string             `json:"model"`
	MaxTokens int                `json:"max_tokens"`
	Messages  []AnthropicMessage `json:"messages"`
}

type AnthropicResponse struct {
	ID      string `json:"id"`
	Type    string `json:"type"`
	Role    string `json:"role"`
	Content []struct {
		Type string `json:"type"`
		Text string `json:"text"`
	} `json:"content"`
	Model        string `json:"model"`
	StopReason   string `json:"stop_reason"`
	StopSequence string `json:"stop_sequence"`
	Usage        struct {
		InputTokens  int `json:"input_tokens"`
		OutputTokens int `json:"output_tokens"`
	} `json:"usage"`
}

type AnthropicError struct {
	Type    string `json:"type"`
	Message string `json:"message"`
}

type AnthropicErrorResponse struct {
	Error AnthropicError `json:"error"`
}

func NewAnthropicClient(apiKey, proxyAddr string) *AnthropicClient {
	client := &http.Client{
		Timeout: 60 * time.Second,
	}

	// Set up proxy if provided
	if proxyAddr != "" && config.Conf.App.ParsedProxy != nil {
		transport := &http.Transport{
			Proxy: http.ProxyURL(config.Conf.App.ParsedProxy),
		}
		client.Transport = transport
	}

	return &AnthropicClient{
		apiKey:     apiKey,
		baseURL:    "https://api.anthropic.com",
		httpClient: client,
	}
}

func (c *AnthropicClient) CreateMessage(prompt, model string) (string, error) {
	if model == "" {
		model = "claude-3-haiku-20240307"
	}

	reqBody := AnthropicRequest{
		Model:     model,
		MaxTokens: 4096,
		Messages: []AnthropicMessage{
			{
				Role:    "user",
				Content: prompt,
			},
		},
	}

	jsonData, err := json.Marshal(reqBody)
	if err != nil {
		return "", fmt.Errorf("failed to marshal request: %v", err)
	}

	req, err := http.NewRequest("POST", c.baseURL+"/v1/messages", bytes.NewBuffer(jsonData))
	if err != nil {
		return "", fmt.Errorf("failed to create request: %v", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("x-api-key", c.apiKey)
	req.Header.Set("anthropic-version", "2023-06-01")

	log.GetLogger().Info("Making Anthropic API request",
		zap.String("model", model),
		zap.String("url", req.URL.String()))

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to make request: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response: %v", err)
	}

	if resp.StatusCode != http.StatusOK {
		var errorResp AnthropicErrorResponse
		if err := json.Unmarshal(body, &errorResp); err != nil {
			return "", fmt.Errorf("API error (status %d): %s", resp.StatusCode, string(body))
		}
		return "", fmt.Errorf("Anthropic API error: %s", errorResp.Error.Message)
	}

	var anthropicResp AnthropicResponse
	if err := json.Unmarshal(body, &anthropicResp); err != nil {
		return "", fmt.Errorf("failed to unmarshal response: %v", err)
	}

	if len(anthropicResp.Content) == 0 {
		return "", fmt.Errorf("no content in response")
	}

	log.GetLogger().Info("Anthropic API response received",
		zap.String("model", anthropicResp.Model),
		zap.Int("input_tokens", anthropicResp.Usage.InputTokens),
		zap.Int("output_tokens", anthropicResp.Usage.OutputTokens))

	return anthropicResp.Content[0].Text, nil
}

func (c *AnthropicClient) SetProxy(proxyURL string) error {
	if proxyURL == "" {
		c.httpClient.Transport = nil
		return nil
	}

	parsedURL, err := url.Parse(proxyURL)
	if err != nil {
		return fmt.Errorf("invalid proxy URL: %v", err)
	}

	transport := &http.Transport{
		Proxy: http.ProxyURL(parsedURL),
	}
	c.httpClient.Transport = transport

	return nil
}
