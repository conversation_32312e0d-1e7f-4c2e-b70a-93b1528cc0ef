# Chat API Documentation

## Overview

The Chat API provides endpoints for interacting with multiple LLM providers including OpenAI, Aliyun (Qwen), and Anthropic (Claude). It supports both streaming and non-streaming responses, multiple input formats, and provider testing.

## Endpoints

### 1. Chat Completion (JSON)

**POST** `/api/chat/completion`

Primary endpoint for chat completions with full JSON support.

#### Request Body (JSON)

```json
{
  "provider": "openai",
  "prompt": "Your prompt here",
  "model": "gpt-4o-mini",
  "stream": false
}
```

#### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `provider` | String | Yes | Provider name: `openai`, `aliyun`, `anthropic` |
| `prompt` | String | Yes | User prompt/question |
| `model` | String | No | Model override (uses default if not specified) |
| `stream` | Boolean | No | Enable streaming response (default: false) |

#### Example Request

```bash
curl -X POST 'http://localhost:8888/api/chat/completion' \
  --header 'Content-Type: application/json' \
  --data '{
    "provider": "openai",
    "prompt": "Explain quantum computing in simple terms",
    "model": "gpt-4o-mini",
    "stream": false
  }'
```

#### Response

```json
{
  "error": 0,
  "msg": "Success",
  "data": {
    "response": "Quantum computing is a revolutionary technology...",
    "provider": "openai",
    "model": "gpt-4o-mini",
    "usage": {
      "prompt_tokens": 12,
      "completion_tokens": 150,
      "total_tokens": 162
    }
  }
}
```

### 2. Simple Chat (Form Data)

**POST** `/api/chat/simple`

Simplified endpoint accepting form data.

#### Parameters (Form Data)

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `provider` | String | Yes | Provider name |
| `prompt` | String | Yes | User prompt |
| `model` | String | No | Model override |

#### Example Request

```bash
curl -X POST 'http://localhost:8888/api/chat/simple' \
  --form 'provider=openai' \
  --form 'prompt=Hello, how are you?' \
  --form 'model=gpt-4o-mini'
```

### 3. Curl-Friendly Chat

**POST** `/api/chat`

Most flexible endpoint supporting both JSON and form data.

#### Example Requests

**Form Data:**
```bash
curl -X POST 'http://localhost:8888/api/chat' \
  --form 'provider=openai' \
  --form 'prompt=Write a short poem about AI'
```

**JSON:**
```bash
curl -X POST 'http://localhost:8888/api/chat' \
  --header 'Content-Type: application/json' \
  --data '{"provider": "openai", "prompt": "Hello!"}'
```

### 4. Get Available Providers

**GET** `/api/chat/providers`

Returns list of available providers and their configuration.

#### Example Request

```bash
curl -X GET 'http://localhost:8888/api/chat/providers'
```

#### Response

```json
{
  "error": 0,
  "msg": "Success",
  "data": {
    "providers": [
      {
        "name": "openai",
        "base_url": "https://api.openai.com/v1",
        "model": "gpt-4o-mini",
        "enabled": true
      },
      {
        "name": "aliyun",
        "base_url": "https://dashscope.aliyuncs.com/compatible-mode/v1",
        "model": "qwen-plus",
        "enabled": true
      },
      {
        "name": "anthropic",
        "base_url": "https://api.anthropic.com",
        "model": "claude-3-haiku-20240307",
        "enabled": true
      }
    ]
  }
}
```

### 5. Test Provider

**GET** `/api/chat/test`

Tests connectivity and functionality of a specific provider.

#### Parameters (Query)

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `provider` | String | Yes | Provider to test |
| `model` | String | No | Model to test with |

#### Example Request

```bash
curl -X GET 'http://localhost:8888/api/chat/test?provider=openai&model=gpt-4o-mini'
```

## Supported Providers

### OpenAI
- **Provider Name**: `openai`
- **Default Model**: `gpt-4o-mini`
- **Supported Models**: `gpt-4o`, `gpt-4o-mini`, `gpt-4`, `gpt-3.5-turbo`
- **Features**: Streaming, Usage tracking

### Aliyun (Qwen)
- **Provider Name**: `aliyun` or `qwen`
- **Default Model**: `qwen-plus`
- **Supported Models**: `qwen-plus`, `qwen-turbo`, `qwen-max`
- **Features**: Usage tracking

### Anthropic (Claude)
- **Provider Name**: `anthropic` or `claude`
- **Default Model**: `claude-3-haiku-20240307`
- **Supported Models**: `claude-3-haiku-20240307`, `claude-3-sonnet-20240229`, `claude-3-opus-20240229`
- **Features**: High-quality responses

## Streaming

Enable streaming by setting `"stream": true` in the request. The response will be sent as Server-Sent Events (SSE).

### Streaming Example

```bash
curl -X POST 'http://localhost:8888/api/chat/completion' \
  --header 'Content-Type: application/json' \
  --data '{
    "provider": "openai",
    "prompt": "Count from 1 to 10",
    "stream": true
  }'
```

### Streaming Response Format

```
data: {"delta": "1", "provider": "openai", "model": "gpt-4o-mini", "done": false}
data: {"delta": ", 2", "provider": "openai", "model": "gpt-4o-mini", "done": false}
...
data: {"delta": "", "provider": "openai", "model": "gpt-4o-mini", "done": true}
```

## Error Handling

### Common Error Responses

**Missing Provider:**
```json
{
  "error": -1,
  "msg": "Provider is required",
  "data": null
}
```

**Missing Prompt:**
```json
{
  "error": -1,
  "msg": "Prompt is required",
  "data": null
}
```

**Unsupported Provider:**
```json
{
  "error": -1,
  "msg": "unsupported provider: invalid_provider",
  "data": null
}
```

**API Error:**
```json
{
  "error": -1,
  "msg": "OpenAI API error: insufficient quota",
  "data": null
}
```

## Configuration

Configure providers in `config/config.toml`:

```toml
[llm]
base_url = "https://api.openai.com/v1"
api_key = "your-api-key"
model = "gpt-4o-mini"

[app]
proxy = "http://proxy:8080"  # Optional proxy
```

## Usage Examples

### Basic Chat
```bash
curl -X POST 'http://localhost:8888/api/chat' \
  --form 'provider=openai' \
  --form 'prompt=Hello, world!'
```

### Code Generation
```bash
curl -X POST 'http://localhost:8888/api/chat' \
  --form 'provider=openai' \
  --form 'prompt=Write a Python function to calculate fibonacci numbers'
```

### Translation
```bash
curl -X POST 'http://localhost:8888/api/chat' \
  --form 'provider=aliyun' \
  --form 'prompt=Translate "Hello, how are you?" to Spanish'
```

### Creative Writing
```bash
curl -X POST 'http://localhost:8888/api/chat' \
  --form 'provider=anthropic' \
  --form 'prompt=Write a short story about a robot learning to paint'
```

## Rate Limits

Rate limits depend on the provider:
- **OpenAI**: Based on your API plan
- **Aliyun**: Based on your account limits
- **Anthropic**: Based on your API tier

## Security

- API keys are configured server-side
- No API keys are exposed in responses
- All requests are logged for monitoring
- Proxy support for corporate environments
