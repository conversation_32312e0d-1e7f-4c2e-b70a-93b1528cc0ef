package service

import (
	"bufio"
	"context"
	"fmt"
	"go.uber.org/zap"
	"krillin-ai/internal/dto"
	"krillin-ai/internal/storage"
	"krillin-ai/internal/types"
	"krillin-ai/log"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
)

func (s Service) embedSrtToVideo(ctx context.Context, stepParam *types.SrtEmbedTaskStepParam, srtOptions dto.SrtOptions) error {
	// Update progress
	s.updateTaskStatus(stepParam.TaskId, types.TaskStatusProcessing, 20, "Converting SRT to ASS format", "", "")

	// Convert SRT to ASS format
	assPath := filepath.Join(stepParam.TaskBasePath, types.SrtEmbedTaskAssFileName)
	if err := s.convertSrtToAss(stepParam.SrtFilePath, assPath, srtOptions); err != nil {
		return fmt.Errorf("failed to convert SRT to ASS: %v", err)
	}

	// Update progress
	s.updateTaskStatus(stepParam.TaskId, types.TaskStatusProcessing, 50, "Embedding subtitles into video", "", "")

	// Embed subtitles using ffmpeg
	if err := s.embedAssToVideo(stepParam.VideoFilePath, assPath, stepParam.OutputPath, srtOptions); err != nil {
		return fmt.Errorf("failed to embed subtitles: %v", err)
	}

	// Update progress
	s.updateTaskStatus(stepParam.TaskId, types.TaskStatusProcessing, 80, "Finalizing video processing", "", "")

	return nil
}

func (s Service) convertSrtToAss(srtPath, assPath string, srtOptions dto.SrtOptions) error {
	// Read SRT file
	srtFile, err := os.Open(srtPath)
	if err != nil {
		return fmt.Errorf("failed to open SRT file: %v", err)
	}
	defer srtFile.Close()

	// Create ASS file
	assFile, err := os.Create(assPath)
	if err != nil {
		return fmt.Errorf("failed to create ASS file: %v", err)
	}
	defer assFile.Close()

	// Write ASS header
	if err := s.writeAssHeader(assFile, srtOptions); err != nil {
		return fmt.Errorf("failed to write ASS header: %v", err)
	}

	// Parse SRT and convert to ASS
	scanner := bufio.NewScanner(srtFile)
	var currentBlock []string

	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())

		if line == "" {
			if len(currentBlock) > 0 {
				if err := s.processSrtBlock(currentBlock, assFile, srtOptions); err != nil {
					return fmt.Errorf("failed to process SRT block: %v", err)
				}
				currentBlock = nil
			}
		} else {
			currentBlock = append(currentBlock, line)
		}
	}

	// Process last block if exists
	if len(currentBlock) > 0 {
		if err := s.processSrtBlock(currentBlock, assFile, srtOptions); err != nil {
			return fmt.Errorf("failed to process last SRT block: %v", err)
		}
	}

	return scanner.Err()
}

func (s Service) writeAssHeader(assFile *os.File, srtOptions dto.SrtOptions) error {
	header := `[Script Info]
Title: Embedded Subtitles
ScriptType: v4.00+

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
Style: Default,%s,%d,&H%s,&H%s,&H%s,&H%s,0,0,0,0,100,100,0,0,1,%d,0,%d,10,10,10,1

[Events]
Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text
`

	// Convert colors from CSS format to ASS format
	primaryColor := convertColorToAss(srtOptions.SubtitleStyle.FontColor)
	backgroundColor := convertColorToAss(srtOptions.SubtitleStyle.BackgroundColor)
	outlineColor := convertColorToAss(srtOptions.SubtitleStyle.OutlineColor)

	// Determine alignment based on position
	alignment := 2 // bottom center
	if srtOptions.SubtitleStyle.Position == "top" {
		alignment = 8 // top center
	}

	// Outline width
	outlineWidth := 0
	if srtOptions.SubtitleStyle.Outline {
		outlineWidth = 2
	}

	formattedHeader := fmt.Sprintf(header,
		srtOptions.SubtitleStyle.FontFamily,
		srtOptions.SubtitleStyle.FontSize,
		primaryColor,
		primaryColor,
		outlineColor,
		backgroundColor,
		outlineWidth,
		alignment,
	)

	_, err := assFile.WriteString(formattedHeader)
	return err
}

func (s Service) processSrtBlock(block []string, assFile *os.File, srtOptions dto.SrtOptions) error {
	if len(block) < 3 {
		return nil // Invalid block
	}

	// Parse timestamp (line 1 is sequence number, line 2 is timestamp)
	timePattern := regexp.MustCompile(`(\d{2}:\d{2}:\d{2},\d{3}) --> (\d{2}:\d{2}:\d{2},\d{3})`)
	matches := timePattern.FindStringSubmatch(block[1])
	if len(matches) != 3 {
		return nil // Invalid timestamp
	}

	startTime := convertSrtTimeToAss(matches[1])
	endTime := convertSrtTimeToAss(matches[2])

	// Combine subtitle text (lines 2 onwards)
	text := strings.Join(block[2:], "\\N")
	text = strings.ReplaceAll(text, "\n", "\\N")

	// Write ASS dialogue line
	dialogue := fmt.Sprintf("Dialogue: 0,%s,%s,Default,,0,0,0,,%s\n", startTime, endTime, text)
	_, err := assFile.WriteString(dialogue)
	return err
}

func (s Service) embedAssToVideo(videoPath, assPath, outputPath string, srtOptions dto.SrtOptions) error {
	// Prepare ffmpeg command
	cmd := exec.Command(storage.FfmpegPath,
		"-y",            // Overwrite output file
		"-i", videoPath, // Input video
		"-vf", fmt.Sprintf("ass=%s", strings.ReplaceAll(assPath, "\\", "/")), // Video filter with ASS subtitles
		"-c:a", "aac", // Audio codec
		"-b:a", "192k", // Audio bitrate
		"-c:v", "libx264", // Video codec
		"-preset", "fast", // Encoding preset
		outputPath, // Output file
	)

	// Execute command
	output, err := cmd.CombinedOutput()
	if err != nil {
		log.GetLogger().Error("ffmpeg command failed",
			zap.String("command", cmd.String()),
			zap.String("output", string(output)),
			zap.Error(err))
		return fmt.Errorf("ffmpeg failed: %v, output: %s", err, string(output))
	}

	log.GetLogger().Info("Successfully embedded subtitles", zap.String("output", outputPath))
	return nil
}

func convertColorToAss(cssColor string) string {
	// Convert CSS color to ASS BGR format
	if strings.HasPrefix(cssColor, "#") {
		// Remove # and convert hex to BGR
		hex := strings.TrimPrefix(cssColor, "#")
		if len(hex) == 6 {
			r := hex[0:2]
			g := hex[2:4]
			b := hex[4:6]
			return b + g + r // BGR format
		}
	}

	// Handle rgba format
	if strings.HasPrefix(cssColor, "rgba") {
		// For simplicity, just return white for rgba
		return "FFFFFF"
	}

	// Default colors
	switch strings.ToLower(cssColor) {
	case "white":
		return "FFFFFF"
	case "black":
		return "000000"
	case "red":
		return "0000FF"
	case "green":
		return "00FF00"
	case "blue":
		return "FF0000"
	default:
		return "FFFFFF" // Default to white
	}
}

func convertSrtTimeToAss(srtTime string) string {
	// Convert SRT time format (HH:MM:SS,mmm) to ASS format (H:MM:SS.cc)
	parts := strings.Split(srtTime, ",")
	if len(parts) != 2 {
		return srtTime
	}

	timePart := parts[0]
	milliseconds := parts[1]

	// Convert milliseconds to centiseconds
	if ms, err := strconv.Atoi(milliseconds); err == nil {
		centiseconds := ms / 10
		return fmt.Sprintf("%s.%02d", timePart, centiseconds)
	}

	return srtTime
}
