#!/bin/bash

# Test script for the new chat API endpoints
# This script tests various chat completion endpoints

echo "Testing Chat API Endpoints..."
echo "=============================="

BASE_URL="http://localhost:8888/api"

# Test 1: Get available providers
echo ""
echo "1. Testing GET /api/chat/providers"
echo "curl -X GET '$BASE_URL/chat/providers'"
echo ""

# Test 2: Simple form-based chat completion
echo "2. Testing POST /api/chat/simple (Form data)"
echo "curl -X POST '$BASE_URL/chat/simple' \\"
echo "  --form 'provider=openai' \\"
echo "  --form 'prompt=Hello, how are you?' \\"
echo "  --form 'model=gpt-4o-mini'"
echo ""

# Test 3: JSON-based chat completion
echo "3. Testing POST /api/chat/completion (JSON)"
echo "curl -X POST '$BASE_URL/chat/completion' \\"
echo "  --header 'Content-Type: application/json' \\"
echo "  --data '{"
echo "    \"provider\": \"openai\","
echo "    \"prompt\": \"Explain quantum computing in simple terms\","
echo "    \"model\": \"gpt-4o-mini\","
echo "    \"stream\": false"
echo "  }'"
echo ""

# Test 4: Curl-friendly endpoint
echo "4. Testing POST /api/chat (Curl-friendly)"
echo "curl -X POST '$BASE_URL/chat' \\"
echo "  --form 'provider=openai' \\"
echo "  --form 'prompt=Write a short poem about AI' \\"
echo "  --form 'model=gpt-4o-mini'"
echo ""

# Test 5: Streaming chat completion
echo "5. Testing POST /api/chat/completion (Streaming)"
echo "curl -X POST '$BASE_URL/chat/completion' \\"
echo "  --header 'Content-Type: application/json' \\"
echo "  --data '{"
echo "    \"provider\": \"openai\","
echo "    \"prompt\": \"Count from 1 to 10\","
echo "    \"model\": \"gpt-4o-mini\","
echo "    \"stream\": true"
echo "  }'"
echo ""

# Test 6: Test different providers
echo "6. Testing different providers"
echo ""
echo "6a. OpenAI provider:"
echo "curl -X POST '$BASE_URL/chat' \\"
echo "  --form 'provider=openai' \\"
echo "  --form 'prompt=Hello from OpenAI!'"
echo ""

echo "6b. Aliyun provider:"
echo "curl -X POST '$BASE_URL/chat' \\"
echo "  --form 'provider=aliyun' \\"
echo "  --form 'prompt=Hello from Aliyun!'"
echo ""

echo "6c. Anthropic provider:"
echo "curl -X POST '$BASE_URL/chat' \\"
echo "  --form 'provider=anthropic' \\"
echo "  --form 'prompt=Hello from Anthropic!'"
echo ""

# Test 7: Provider testing endpoint
echo "7. Testing provider connectivity"
echo ""
echo "7a. Test OpenAI:"
echo "curl -X GET '$BASE_URL/chat/test?provider=openai'"
echo ""

echo "7b. Test Aliyun:"
echo "curl -X GET '$BASE_URL/chat/test?provider=aliyun&model=qwen-plus'"
echo ""

echo "7c. Test Anthropic:"
echo "curl -X GET '$BASE_URL/chat/test?provider=anthropic&model=claude-3-haiku-20240307'"
echo ""

# Test 8: Error handling
echo "8. Testing error handling"
echo ""
echo "8a. Missing provider:"
echo "curl -X POST '$BASE_URL/chat' \\"
echo "  --form 'prompt=Hello!'"
echo ""

echo "8b. Missing prompt:"
echo "curl -X POST '$BASE_URL/chat' \\"
echo "  --form 'provider=openai'"
echo ""

echo "8c. Invalid provider:"
echo "curl -X POST '$BASE_URL/chat' \\"
echo "  --form 'provider=invalid' \\"
echo "  --form 'prompt=Hello!'"
echo ""

# Example responses
echo ""
echo "Expected Response Format:"
echo "========================="
echo ""
echo "Success Response:"
echo '{'
echo '  "error": 0,'
echo '  "msg": "Success",'
echo '  "data": {'
echo '    "response": "Hello! I am doing well, thank you for asking...",'
echo '    "provider": "openai",'
echo '    "model": "gpt-4o-mini",'
echo '    "usage": {'
echo '      "prompt_tokens": 12,'
echo '      "completion_tokens": 25,'
echo '      "total_tokens": 37'
echo '    }'
echo '  }'
echo '}'
echo ""

echo "Error Response:"
echo '{'
echo '  "error": -1,'
echo '  "msg": "Provider is required",'
echo '  "data": null'
echo '}'
echo ""

echo "Providers List Response:"
echo '{'
echo '  "error": 0,'
echo '  "msg": "Success",'
echo '  "data": {'
echo '    "providers": ['
echo '      {'
echo '        "name": "openai",'
echo '        "base_url": "https://api.openai.com/v1",'
echo '        "model": "gpt-4o-mini",'
echo '        "enabled": true'
echo '      },'
echo '      {'
echo '        "name": "aliyun",'
echo '        "base_url": "https://dashscope.aliyuncs.com/compatible-mode/v1",'
echo '        "model": "qwen-plus",'
echo '        "enabled": true'
echo '      }'
echo '    ]'
echo '  }'
echo '}'
echo ""

echo "To run actual tests, uncomment and execute the curl commands above."
echo "Make sure the server is running on localhost:8888 and API keys are configured."
