package cloudflare

import (
	"context"
	"fmt"
	"github.com/aws/aws-sdk-go-v2/service/s3/types"
	"io"
	"mime/multipart"
	"os"
	"path/filepath"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/aws/aws-sdk-go-v2/service/s3"
)

type R2Client struct {
	client *s3.Client
	bucket string
}

func NewR2Client(accessKeyID, secretAccessKey, bucket, endpoint, region string) (*R2Client, error) {
	cfg, err := config.LoadDefaultConfig(context.TODO(),
		config.WithCredentialsProvider(credentials.NewStaticCredentialsProvider(accessKeyID, secretAccessKey, "")),
		config.WithRegion(region),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to load AWS config: %v", err)
	}

	client := s3.NewFromConfig(cfg, func(o *s3.Options) {
		o.BaseEndpoint = aws.String(endpoint)
		o.UsePathStyle = true
	})

	return &R2Client{
		client: client,
		bucket: bucket,
	}, nil
}

// UploadFile uploads a file from local path to Cloudflare R2
func (r *R2Client) UploadFile(ctx context.Context, objectKey, filePath string) (string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", fmt.Errorf("failed to open file: %v", err)
	}
	defer file.Close()

	_, err = r.client.PutObject(ctx, &s3.PutObjectInput{
		Bucket: aws.String(r.bucket),
		Key:    aws.String(objectKey),
		Body:   file,
	})
	if err != nil {
		return "", fmt.Errorf("failed to upload file to R2: %v", err)
	}

	// Return the public URL
	url := fmt.Sprintf("https://%s.r2.cloudflarestorage.com/%s", r.bucket, objectKey)
	fmt.Printf("File %s uploaded successfully to R2 bucket %s as %s\n", filePath, r.bucket, objectKey)
	return url, nil
}

// UploadFileFromMultipart uploads a file from multipart.FileHeader to Cloudflare R2
func (r *R2Client) UploadFileFromMultipart(ctx context.Context, objectKey string, fileHeader *multipart.FileHeader) (string, error) {
	file, err := fileHeader.Open()
	if err != nil {
		return "", fmt.Errorf("failed to open multipart file: %v", err)
	}
	defer file.Close()

	_, err = r.client.PutObject(ctx, &s3.PutObjectInput{
		Bucket: aws.String(r.bucket),
		Key:    aws.String(objectKey),
		Body:   file,
		ACL:    types.ObjectCannedACLPublicRead,
	})
	if err != nil {
		return "", fmt.Errorf("failed to upload file to R2: %v", err)
	}

	// Return the public URL
	url := fmt.Sprintf("%s/%s/%s", *r.client.Options().BaseEndpoint, r.bucket, objectKey)
	fmt.Printf("File %s uploaded successfully to R2 bucket %s as %s\n", fileHeader.Filename, r.bucket, objectKey)
	return url, nil
}

// UploadFileFromReader uploads a file from io.Reader to Cloudflare R2
func (r *R2Client) UploadFileFromReader(ctx context.Context, objectKey string, reader io.Reader) (string, error) {
	_, err := r.client.PutObject(ctx, &s3.PutObjectInput{
		Bucket: aws.String(r.bucket),
		Key:    aws.String(objectKey),
		Body:   reader,
	})
	if err != nil {
		return "", fmt.Errorf("failed to upload file to R2: %v", err)
	}

	// Return the public URL
	url := fmt.Sprintf("https://%s.r2.cloudflarestorage.com/%s", r.bucket, objectKey)
	fmt.Printf("File uploaded successfully to R2 bucket %s as %s\n", r.bucket, objectKey)
	return url, nil
}

// GenerateObjectKey generates a unique object key for the file
func GenerateObjectKey(filename string) string {
	ext := filepath.Ext(filename)
	base := filename[:len(filename)-len(ext)]
	// You can add timestamp or UUID here for uniqueness
	return fmt.Sprintf("%s_%d%s", base,
		// Simple timestamp for uniqueness
		// You might want to use a proper UUID library
		len(filename), ext)
}
