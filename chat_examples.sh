#!/bin/bash

# Simple examples for the Chat API
# Make sure the server is running on localhost:8888

BASE_URL="http://localhost:8888/api"

echo "Chat API Examples"
echo "=================="
echo ""

# Example 1: Simple question
echo "Example 1: Simple question to OpenAI"
echo "Command:"
echo "curl -X POST '$BASE_URL/chat' --form 'provider=openai' --form 'prompt=What is the capital of France?'"
echo ""

# Example 2: Code generation
echo "Example 2: Code generation with <PERSON>yun"
echo "Command:"
echo "curl -X POST '$BASE_URL/chat' --form 'provider=aliyun' --form 'prompt=Write a Python function to reverse a string'"
echo ""

# Example 3: Creative writing
echo "Example 3: Creative writing with Anthropic"
echo "Command:"
echo "curl -X POST '$BASE_URL/chat' --form 'provider=anthropic' --form 'prompt=Write a haiku about programming'"
echo ""

# Example 4: JSON format
echo "Example 4: JSON format request"
echo "Command:"
echo "curl -X POST '$BASE_URL/chat/completion' \\"
echo "  --header 'Content-Type: application/json' \\"
echo "  --data '{\"provider\": \"openai\", \"prompt\": \"Explain machine learning in one sentence\"}'"
echo ""

# Example 5: Check providers
echo "Example 5: Check available providers"
echo "Command:"
echo "curl -X GET '$BASE_URL/chat/providers'"
echo ""

# Example 6: Test a provider
echo "Example 6: Test OpenAI provider"
echo "Command:"
echo "curl -X GET '$BASE_URL/chat/test?provider=openai'"
echo ""

echo "To run these examples, copy and paste the commands above."
echo "Make sure to configure your API keys in config/config.toml first."
