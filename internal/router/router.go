package router

import (
	"krillin-ai/internal/handler"
	"krillin-ai/static"
	"net/http"

	"github.com/gin-gonic/gin"
)

func CORSMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		origin := c.<PERSON>eader("Origin")
		if origin != "" {
			c.<PERSON>.Header().Set("Access-Control-Allow-Origin", origin) // Chỉ định origin cụ thể
		} else {
			c.Writer.Header().Set("Access-Control-Allow-Origin", "*") // fallback nếu không có Origin
		}

		c.Writer.Header().Set("Access-Control-Allow-Methods", "POST, GET, OPTIONS, PUT, DELETE, UPDATE")
		c.<PERSON>.Header().Set("Access-Control-Allow-Headers", "Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, X-Max")
		c.Writer.Header().Set("Access-Control-Allow-Credentials", "true")
		c.Writer.Header().Set("Access-Control-Max-Age", "86400")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204) // 204 là chuẩn cho preflight response
			return
		}

		c.Next()
	}
}

func SetupRouter(r *gin.Engine) {
	r.Use(CORSMiddleware())
	api := r.Group("/api")
	hdl := handler.NewHandler()
	{
		api.POST("/capability/subtitleTask", hdl.StartSubtitleTask)
		api.POST("/capability/executeAction", hdl.ExecuteAction)
		api.GET("/capability/subtitleTask", hdl.GetSubtitleTask)
		api.POST("/file", hdl.UploadFile)
		api.POST("/file2", hdl.UploadFile2)
		api.GET("/file/*filepath", hdl.DownloadFile)
	}

	r.GET("/", func(c *gin.Context) {
		c.Redirect(http.StatusMovedPermanently, "/static")
	})
	r.StaticFS("/static", http.FS(static.EmbeddedFiles))
}
