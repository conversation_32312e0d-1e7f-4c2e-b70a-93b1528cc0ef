<div align="center">
  <img src="/docs/images/logo.png" alt="KrillinAI" height="90">

  # Минималистичный инструмент для перевода и озвучивания видео с AI

  <a href="https://trendshift.io/repositories/13360" target="_blank"><img src="https://trendshift.io/api/badge/repositories/13360" alt="krillinai%2FKrillinAI | Trendshift" style="width: 250px; height: 55px;" width="250" height="55"/></a>

  **[English](/README.md)｜[简体中文](/docs/zh/README.md)｜[日本語](/docs/jp/README.md)｜[한국어](/docs/kr/README.md)｜[Tiếng Việt](/docs/vi/README.md)｜[Français](/docs/fr/README.md)｜[Deutsch](/docs/de/README.md)｜[Español](/docs/es/README.md)｜[Português](/docs/pt/README.md)｜[Русский](/docs/rus/README.md)｜[اللغة العربية](/docs/ar/README.md)**

[![Twitter](https://img.shields.io/badge/Twitter-KrillinAI-orange?logo=twitter)](https://x.com/KrillinAI)
[![QQ 群](https://img.shields.io/badge/QQ%20群-754069680-green?logo=tencent-qq)](https://jq.qq.com/?_wv=1027&k=754069680)
[![Bilibili](https://img.shields.io/badge/dynamic/json?label=Bilibili&query=%24.data.follower&suffix=粉丝&url=https%3A%2F%2Fapi.bilibili.com%2Fx%2Frelation%2Fstat%3Fvmid%3D242124650&logo=bilibili&color=00A1D6&labelColor=FE7398&logoColor=FFFFFF)](https://space.bilibili.com/242124650)

</div>

 ## Описание проекта  ([Попробуйте онлайн-версию сейчас!](https://www.klic.studio/))

Krillin AI — это универсальное решение для локализации и улучшения аудио и видео. Этот простой, но мощный инструмент объединяет перевод видео, озвучивание и клонирование голоса, поддерживает вывод в горизонтальном и вертикальном формате, обеспечивая идеальное представление на всех основных платформах (Bilibili, Xiaohongshu, Douyin, WeChat Video, Kuaishou, YouTube, TikTok и др.). С помощью рабочего процесса от начала до конца Krillin AI может всего за несколько кликов преобразовать исходные материалы в готовый к использованию кроссплатформенный контент.

## Основные характеристики и функции:
🎯 **Запуск в один клик**: Не требует сложной настройки окружения, автоматически устанавливает зависимости и готов к использованию, добавлена версия для рабочего стола для удобства!

📥 **Получение видео**: Поддерживает загрузку через yt-dlp или загрузку локальных файлов

📜 **Точное распознавание**: Высокая точность распознавания речи на основе Whisper

🧠 **Интеллектуальное разделение**: Использует LLM для разделения и выравнивания субтитров

🔄 **Замена терминов**: Замена профессиональной лексики в один клик 

🌍 **Профессиональный перевод**: Перевод с контекстом с использованием LLM для сохранения естественности семантики

🎙️ **Клонирование голоса**: Предоставляет отборные голоса CosyVoice или возможность клонирования пользовательского голоса

🎬 **Синтез видео**: Автоматическая обработка видео в горизонтальном и вертикальном формате и компоновка субтитров

💻 **Кроссплатформенность**: Поддерживает Windows, Linux, macOS, предоставляет версии для рабочего стола и сервера


## Примеры работы
На изображении ниже показан результат генерации файла субтитров после импорта локального видео длительностью 46 минут, выполненного в один клик, без каких-либо ручных корректировок. Нет пропусков, наложений, паузы естественные, качество перевода также очень высокое.
![Эффект выравнивания](/docs/images/alignment.png)

<table>
<tr>
<td width="33%">

### Перевод субтитров
---
https://github.com/user-attachments/assets/bba1ac0a-fe6b-4947-b58d-ba99306d0339

</td>
<td width="33%">



### Озвучивание
---
https://github.com/user-attachments/assets/0b32fad3-c3ad-4b6a-abf0-0865f0dd2385

</td>

<td width="33%">

### Вертикальный экран
---
https://github.com/user-attachments/assets/c2c7b528-0ef8-4ba9-b8ac-f9f92f6d4e71

</td>

</tr>
</table>

## 🔍 Поддержка услуг распознавания речи
_**Все локальные модели в таблице поддерживают автоматическую установку исполняемых файлов + файлов модели, вам нужно только выбрать, а KrillinAI подготовит все остальное.**_

| Источник услуг        | Поддерживаемые платформы | Доступные модели                             | Локально/в облаке | Примечания          |
|--------------------|-----------------|----------------------------------------|-------|-------------|
| **OpenAI Whisper** | Все платформы   | -                                      | В облаке | Быстро и качественно      |
| **FasterWhisper**  | Windows/Linux   | `tiny`/`medium`/`large-v2` (рекомендуется medium+) | Локально | Быстрее, без облачных затрат |
| **WhisperKit**     | macOS (только для чипов M) | `large-v2`                             | Локально | Оптимизация для Apple чипов |
| **WhisperCpp**     | Все платформы   | `large-v2`                             | Локально | Поддержка всех платформ       |
| **Alibaba Cloud ASR** | Все платформы   | -                                      | В облаке | Избегайте проблем с сетью в Китае  |

## 🚀 Поддержка больших языковых моделей

✅ Совместимость со всеми облачными/локальными большими языковыми моделями, соответствующими **OpenAI API**, включая, но не ограничиваясь:
- OpenAI
- Gemini
- DeepSeek
- Tongyi Qianwen
- Локально развернутые открытые модели
- Другие API-сервисы, совместимые с форматом OpenAI

## 🎤 Поддержка TTS (текст в речь)
- Услуги речи Alibaba Cloud
- OpenAI TTS

## Поддержка языков
Поддерживаемые языки ввода: китайский, английский, японский, немецкий, турецкий, корейский, русский, малайский (постоянно добавляются новые)

Поддерживаемые языки перевода: английский, китайский, русский, испанский, французский и еще 101 язык

## Предварительный просмотр интерфейса
![Предварительный просмотр интерфейса](/docs/images/ui_desktop.png)


## 🚀 Быстрый старт
### Основные шаги
Сначала загрузите [Release](https://github.com/krillinai/KrillinAI/releases) с исполняемым файлом, соответствующим вашей системе, следуя приведенным ниже инструкциям, выберите версию для рабочего стола или не для рабочего стола, затем поместите в пустую папку, загрузите программное обеспечение в пустую папку, так как после запуска будут созданы некоторые каталоги, размещение в пустой папке упростит управление.  

【Если это версия для рабочего стола, то смотрите здесь, если файл release содержит desktop】  
_Версия для рабочего стола только что выпущена, чтобы решить проблему с тем, что новичкам сложно правильно редактировать конфигурационные файлы, также есть некоторые ошибки, которые продолжают обновляться_
1. Дважды щелкните файл, чтобы начать использовать (версия для рабочего стола также требует настройки внутри программы)

【Если это не версия для рабочего стола, то смотрите здесь, если файл release не содержит desktop】  
_Не версия для рабочего стола — это начальная версия, настройка более сложная, но функции стабильны, также подходит для развертывания на сервере, так как будет предоставлен интерфейс в веб-формате_
1. Создайте папку `config` внутри папки, затем создайте файл `config.toml` в папке `config`, скопируйте содержимое файла `config-example.toml` из каталога `config` и заполните его, следуя комментариям, вашими конфигурационными данными.
2. Дважды щелкните или выполните исполняемый файл в терминале, чтобы запустить службу 
3. Откройте браузер, введите `http://127.0.0.1:8888`, чтобы начать использовать (замените 8888 на порт, указанный в вашем конфигурационном файле)

### Для пользователей macOS
【Если это версия для рабочего стола, то смотрите здесь, если файл release содержит desktop】  
В настоящее время упаковка для рабочего стола из-за проблем с подписью и т. д. не позволяет запускать двойным щелчком или устанавливать dmg, необходимо вручную доверять приложению, вот как:
1. Откройте терминал в каталоге, где находится исполняемый файл (предположим, имя файла — KrillinAI_1.0.0_desktop_macOS_arm64)
2. Выполните поочередно следующие команды:
```
sudo xattr -cr ./KrillinAI_1.0.0_desktop_macOS_arm64
sudo chmod +x ./KrillinAI_1.0.0_desktop_macOS_arm64 
./KrillinAI_1.0.0_desktop_macOS_arm64
```

【Если это не версия для рабочего стола, то смотрите здесь, если файл release не содержит desktop】  
Это программное обеспечение не подписано, поэтому при запуске на macOS после завершения настройки файлов в "Основных шагах" также необходимо вручную доверять приложению, вот как:
1. Откройте терминал в каталоге, где находится исполняемый файл (предположим, имя файла — KrillinAI_1.0.0_macOS_arm64)
2. Выполните поочередно следующие команды:
   ```
    sudo xattr -rd com.apple.quarantine ./KrillinAI_1.0.0_macOS_arm64
    sudo chmod +x ./KrillinAI_1.0.0_macOS_arm64
    ./KrillinAI_1.0.0_macOS_arm64
    ```
    Это запустит службу

### Развертывание с помощью Docker
Этот проект поддерживает развертывание с помощью Docker, пожалуйста, ознакомьтесь с [инструкцией по развертыванию Docker](./docker.md)

### Описание конфигурации Cookie (необязательно)

Если вы столкнулись с проблемой неудачной загрузки видео

Пожалуйста, ознакомьтесь с [описанием конфигурации Cookie](./get_cookies.md) для настройки вашей информации о Cookie.

### Помощь с конфигурацией (обязательно к прочтению)
Самый быстрый и удобный способ настройки:
* Заполните `transcribe.provider.name` значением `openai`, тогда вам нужно будет заполнить только блоки `transcribe.openai` и конфигурацию большой модели в блоке `llm` для перевода субтитров. (`app.proxy`, `model` и `openai.base_url` заполняйте по своему усмотрению)

Способ настройки с использованием локальной модели распознавания речи (учитывающий стоимость, скорость и качество)
* Заполните `transcribe.provider.name` значением `fasterwhisper`, `transcribe.fasterwhisper.model` заполните значением `large-v2`, затем заполните `llm` для конфигурации большой модели, чтобы начать перевод субтитров, локальная модель будет автоматически загружена и установлена. (`app.proxy` и `openai.base_url` аналогично)

Текст в речь (TTS) является необязательным, логика настройки такая же, заполните `tts.provider.name`, затем заполните соответствующий блок конфигурации под `tts`, код звука в UI заполняйте в соответствии с документацией выбранного поставщика. Заполнение таких данных, как aksk для Alibaba Cloud, может повторяться, это сделано для обеспечения ясности структуры конфигурации.  
Обратите внимание: если вы используете клонирование голоса, `tts` поддерживает только выбор `aliyun`.

**Для получения AccessKey, Bucket, AppKey для Alibaba Cloud, пожалуйста, ознакомьтесь с**: [описанием конфигурации Alibaba Cloud](./aliyun.md) 

Пожалуйста, поймите, что задача = распознавание речи + перевод большой модели + услуги речи (TTS и т. д., необязательно), это поможет вам понять конфигурационный файл.

## Часто задаваемые вопросы

Пожалуйста, перейдите к [часто задаваемым вопросам](./faq.md)

## Правила участия
1. Не отправляйте бесполезные файлы, такие как .vscode, .idea и т. д., используйте .gitignore для фильтрации
2. Не отправляйте config.toml, вместо этого отправьте config-example.toml

## Свяжитесь с нами
1. Присоединяйтесь к нашей группе QQ для получения ответов на вопросы: 754069680
2. Подписывайтесь на наши социальные сети, [Bilibili](https://space.bilibili.com/242124650), ежедневно делимся качественным контентом в области AI технологий

## История звезд

[![Star History Chart](https://api.star-history.com/svg?repos=krillinai/KrillinAI&type=Date)](https://star-history.com/#krillinai/KrillinAI&Date)