package dto

type SubtitleStyle struct {
	FontFamily      string `json:"fontFamily"`
	FontSize        int    `json:"fontSize"`
	FontColor       string `json:"fontColor"`
	BackgroundColor string `json:"backgroundColor"`
	Position        string `json:"position"`
	Outline         bool   `json:"outline"`
	OutlineColor    string `json:"outlineColor"`
}

type SrtOptions struct {
	EmbedOriginalSubtitles    bool          `json:"embedOriginalSubtitles"`
	EmbedTranslatedSubtitles  bool          `json:"embedTranslatedSubtitles"`
	SubtitleStyle             SubtitleStyle `json:"subtitleStyle"`
	OutputFormat              string        `json:"outputFormat"`
}

type CreateTaskReq struct {
	VideoFile  string     `form:"videoFile" binding:"required"`
	SrtFile    string     `form:"srtFile" binding:"required"`
	SrtOptions SrtOptions `form:"srtOptions" binding:"required"`
}

type CreateTaskRes struct {
	TaskId string `json:"task_id"`
}

type TaskStatus struct {
	TaskId         string `json:"task_id"`
	Status         string `json:"status"` // created, processing, completed, failed
	ProcessPercent int    `json:"process_percent"`
	Message        string `json:"message"`
	VideoUrl       string `json:"video_url,omitempty"`
	Error          string `json:"error,omitempty"`
}
