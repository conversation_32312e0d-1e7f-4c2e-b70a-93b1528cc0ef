# Task API Documentation

## Overview

The Task API provides endpoints for creating and managing video subtitle embedding tasks. This API allows you to upload a video file and an SRT subtitle file, then embed the subtitles into the video with customizable styling options.

## Endpoints

### 1. Create Task

**POST** `/api/capability/task`

Creates a new task to embed SRT subtitles into a video file.

#### Request

**Content-Type:** `multipart/form-data`

**Parameters:**

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `videoFile` | File | Yes | Video file to embed subtitles into (mp4, avi, mov, mkv, webm, flv) |
| `srtFile` | File | Yes | SRT subtitle file (.srt) |
| `srtOptions` | JSON String | Yes | Subtitle styling and embedding options |

#### SRT Options Structure

```json
{
  "embedOriginalSubtitles": true,
  "embedTranslatedSubtitles": true,
  "subtitleStyle": {
    "fontFamily": "Arial",
    "fontSize": 18,
    "fontColor": "white",
    "backgroundColor": "rgba(0,0,0,0.8)",
    "position": "bottom",
    "outline": true,
    "outlineColor": "black"
  },
  "outputFormat": "mp4"
}
```

#### Example Request

```bash
curl --location 'http://localhost:8888/api/capability/task' \
  --form 'videoFile=@"/path/to/video.mp4"' \
  --form 'srtFile=@"/path/to/subtitles.srt"' \
  --form 'srtOptions="{\"embedOriginalSubtitles\":true,\"embedTranslatedSubtitles\":true,\"subtitleStyle\":{\"fontFamily\":\"Arial\",\"fontSize\":18,\"fontColor\":\"white\",\"backgroundColor\":\"rgba(0,0,0,0.8)\",\"position\":\"bottom\",\"outline\":true,\"outlineColor\":\"black\"},\"outputFormat\":\"mp4\"}"'
```

#### Response

**Success (200):**
```json
{
  "error": 0,
  "msg": "Task created successfully",
  "data": {
    "task_id": "1748280809_XfF85wS"
  }
}
```

**Error (200):**
```json
{
  "error": -1,
  "msg": "Error message",
  "data": null
}
```

### 2. Get Task Status

**GET** `/api/capability/task?taskId={task_id}`

Retrieves the status of a specific task.

#### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `taskId` | String | Yes | Task ID returned from create task endpoint |

#### Example Request

```bash
curl 'http://localhost:8888/api/capability/task?taskId=1748280809_XfF85wS'
```

#### Response

**Success (200):**
```json
{
  "error": 0,
  "msg": "Success",
  "data": {
    "task_id": "1748280809_XfF85wS",
    "status": "completed",
    "process_percent": 100,
    "message": "Task completed successfully",
    "video_url": "https://your-r2-bucket.com/processed/video.mp4"
  }
}
```

#### Task Status Values

| Status | Description |
|--------|-------------|
| `created` | Task has been created and queued |
| `processing` | Task is currently being processed |
| `completed` | Task completed successfully |
| `failed` | Task failed with an error |

## Implementation Details

### Background Processing

1. **Parse Request**: Extract video file, SRT file, and options
2. **Embed SRT to Video**: Convert SRT to ASS format and embed using FFmpeg
3. **Upload to R2**: Upload processed video to Cloudflare R2 (if configured)
4. **Return URL**: Provide download URL for the processed video

### File Support

**Video Formats:**
- MP4
- AVI
- MOV
- MKV
- WebM
- FLV

**Subtitle Formats:**
- SRT

### Styling Options

The subtitle styling supports:
- Font family and size
- Font and outline colors
- Background color (including transparency)
- Position (top/bottom)
- Outline enable/disable

### Error Handling

Common error scenarios:
- Invalid file formats
- Missing required parameters
- FFmpeg processing errors
- File upload failures
- R2 upload failures (if configured)

## Configuration

Ensure the following are configured in `config/config.toml`:

1. **FFmpeg**: Path to FFmpeg binary
2. **Cloudflare R2** (optional): For video upload and hosting
3. **Redis**: For task storage and status tracking

## Dependencies

- FFmpeg for video processing
- Redis for task storage
- Cloudflare R2 (optional) for file hosting
