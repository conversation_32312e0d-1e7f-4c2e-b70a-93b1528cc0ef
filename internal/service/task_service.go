package service

import (
	"context"
	"encoding/json"
	"fmt"
	"mime/multipart"
	"os"
	"path/filepath"
	"time"

	"go.uber.org/zap"
	"krillin-ai/internal/dto"
	"krillin-ai/internal/storage"
	"krillin-ai/internal/types"
	"krillin-ai/log"
	"krillin-ai/pkg/util"
)

func (s Service) CreateSrtEmbedTask(videoFile, srtFile *multipart.FileHeader, srtOptions dto.SrtOptions) (*dto.CreateTaskRes, error) {
	// Generate task ID
	taskId := generateTaskId()

	// Create task directory
	taskBasePath := filepath.Join("./tasks", taskId)
	if err := os.MkdirAll(filepath.Join(taskBasePath, "output"), os.ModePerm); err != nil {
		return nil, fmt.Errorf("failed to create task directory: %v", err)
	}

	// Save video file
	videoPath := filepath.Join(taskBasePath, types.SrtEmbedTaskVideoFileName)
	if err := saveMultipartFile(videoFile, videoPath); err != nil {
		return nil, fmt.Errorf("failed to save video file: %v", err)
	}

	// Save SRT file
	srtPath := filepath.Join(taskBasePath, types.SrtEmbedTaskSrtFileName)
	if err := saveMultipartFile(srtFile, srtPath); err != nil {
		return nil, fmt.Errorf("failed to save SRT file: %v", err)
	}

	// Serialize SRT options
	srtOptionsJson, err := json.Marshal(srtOptions)
	if err != nil {
		return nil, fmt.Errorf("failed to serialize SRT options: %v", err)
	}

	// Create task
	task := &types.SrtEmbedTask{
		TaskId:         taskId,
		Status:         types.TaskStatusCreated,
		ProcessPercent: 0,
		Message:        "Task created successfully",
		VideoFilePath:  videoPath,
		SrtFilePath:    srtPath,
		SrtOptions:     string(srtOptionsJson),
		OutputPath:     filepath.Join(taskBasePath, "output", types.SrtEmbedTaskOutputFileName),
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}

	// Store task in storage
	storage.SrtEmbedTasks.Store(taskId, task)

	// Start background processing
	go s.processSrtEmbedTask(taskId)

	return &dto.CreateTaskRes{
		TaskId: taskId,
	}, nil
}

func (s Service) GetSrtEmbedTaskStatus(taskId string) (*dto.TaskStatus, error) {
	task := &types.SrtEmbedTask{}
	err := storage.SrtEmbedTasks.Load(taskId, task)
	if err != nil {
		return nil, fmt.Errorf("failed to load task: %v", err)
	}

	if task.TaskId == "" {
		return nil, fmt.Errorf("task not found")
	}

	status := &dto.TaskStatus{
		TaskId:         task.TaskId,
		Status:         task.Status,
		ProcessPercent: task.ProcessPercent,
		Message:        task.Message,
		VideoUrl:       task.VideoUrl,
		Error:          task.Error,
	}

	return status, nil
}

func (s Service) processSrtEmbedTask(taskId string) {
	defer func() {
		if r := recover(); r != nil {
			log.GetLogger().Error("processSrtEmbedTask panic", zap.String("taskId", taskId), zap.Any("panic", r))
			s.updateTaskStatus(taskId, types.TaskStatusFailed, 0, "Task failed due to panic", "", fmt.Sprintf("Panic: %v", r))
		}
	}()

	log.GetLogger().Info("Starting SRT embed task processing", zap.String("taskId", taskId))

	// Load task
	task := &types.SrtEmbedTask{}
	err := storage.SrtEmbedTasks.Load(taskId, task)
	if err != nil {
		log.GetLogger().Error("Failed to load task", zap.String("taskId", taskId), zap.Error(err))
		return
	}

	// Update status to processing
	s.updateTaskStatus(taskId, types.TaskStatusProcessing, 10, "Starting video processing", "", "")

	// Parse SRT options
	var srtOptions dto.SrtOptions
	if err := json.Unmarshal([]byte(task.SrtOptions), &srtOptions); err != nil {
		log.GetLogger().Error("Failed to parse SRT options", zap.String("taskId", taskId), zap.Error(err))
		s.updateTaskStatus(taskId, types.TaskStatusFailed, 0, "Failed to parse SRT options", "", err.Error())
		return
	}

	// Create step param
	stepParam := &types.SrtEmbedTaskStepParam{
		TaskId:         taskId,
		TaskPtr:        task,
		TaskBasePath:   filepath.Dir(task.VideoFilePath),
		VideoFilePath:  task.VideoFilePath,
		SrtFilePath:    task.SrtFilePath,
		SrtOptionsJson: task.SrtOptions,
		OutputPath:     task.OutputPath,
	}

	// Process the task
	ctx := context.Background()
	if err := s.embedSrtToVideo(ctx, stepParam, srtOptions); err != nil {
		log.GetLogger().Error("Failed to embed SRT to video", zap.String("taskId", taskId), zap.Error(err))
		s.updateTaskStatus(taskId, types.TaskStatusFailed, 0, "Failed to embed subtitles", "", err.Error())
		return
	}

	// Upload to R2 if configured
	var videoUrl string
	if s.R2Client != nil {
		s.updateTaskStatus(taskId, types.TaskStatusProcessing, 90, "Uploading video to cloud storage", "", "")
		
		timestamp := time.Now().Unix()
		objectKey := fmt.Sprintf("processed/%d_%s_%s", timestamp, taskId, types.SrtEmbedTaskOutputFileName)
		
		url, err := s.R2Client.UploadFile(ctx, objectKey, task.OutputPath)
		if err != nil {
			log.GetLogger().Error("Failed to upload video to R2", zap.String("taskId", taskId), zap.Error(err))
			s.updateTaskStatus(taskId, types.TaskStatusFailed, 0, "Failed to upload video", "", err.Error())
			return
		}
		videoUrl = url
	}

	// Task completed
	s.updateTaskStatus(taskId, types.TaskStatusCompleted, 100, "Task completed successfully", videoUrl, "")
	log.GetLogger().Info("SRT embed task completed", zap.String("taskId", taskId))
}

func (s Service) updateTaskStatus(taskId, status string, processPercent int, message, videoUrl, errorMsg string) {
	task := &types.SrtEmbedTask{}
	err := storage.SrtEmbedTasks.Load(taskId, task)
	if err != nil {
		log.GetLogger().Error("Failed to load task for status update", zap.String("taskId", taskId), zap.Error(err))
		return
	}

	task.Status = status
	task.ProcessPercent = processPercent
	task.Message = message
	task.VideoUrl = videoUrl
	task.Error = errorMsg
	task.UpdatedAt = time.Now()

	storage.SrtEmbedTasks.Store(taskId, task)
}

func saveMultipartFile(fileHeader *multipart.FileHeader, destPath string) error {
	src, err := fileHeader.Open()
	if err != nil {
		return err
	}
	defer src.Close()

	dst, err := os.Create(destPath)
	if err != nil {
		return err
	}
	defer dst.Close()

	_, err = dst.ReadFrom(src)
	return err
}

func generateTaskId() string {
	timestamp := time.Now().Unix()
	randomStr := util.GenerateRandStringWithUpperLowerNum(8)
	return fmt.Sprintf("%d_%s", timestamp, randomStr)
}
