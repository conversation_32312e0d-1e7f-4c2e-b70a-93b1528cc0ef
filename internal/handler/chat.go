package handler

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"krillin-ai/internal/dto"
	"krillin-ai/internal/response"
	"krillin-ai/internal/service"
	"krillin-ai/log"
)

type ChatHandler struct {
	chatService *service.ChatService
}

func NewChatHandler() *ChatHandler {
	return &ChatHandler{
		chatService: service.NewChatService(),
	}
}

func (h *<PERSON>t<PERSON>and<PERSON>) ChatCompletion(c *gin.Context) {
	var req dto.ChatRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.GetLogger().Error("ChatCompletion failed to bind JSON", zap.Error(err))
		response.R(c, response.Response{
			Error: -1,
			Msg:   "Invalid request format: " + err.Error(),
			Data:  nil,
		})
		return
	}

	// Validate provider
	if req.Provider == "" {
		response.R(c, response.Response{
			Error: -1,
			Msg:   "Provider is required",
			Data:  nil,
		})
		return
	}

	// Validate prompt
	if req.Prompt == "" {
		response.R(c, response.Response{
			Error: -1,
			Msg:   "Prompt is required",
			Data:  nil,
		})
		return
	}

	log.GetLogger().Info("Processing chat completion request",
		zap.String("provider", req.Provider),
		zap.String("model", req.Model),
		zap.Bool("stream", req.Stream),
		zap.Int("prompt_length", len(req.Prompt)))

	// Handle streaming vs non-streaming
	if req.Stream {
		h.handleStreamingChat(c, req)
		return
	}

	// Non-streaming response
	resp, err := h.chatService.ChatCompletion(req)
	if err != nil {
		log.GetLogger().Error("ChatCompletion failed",
			zap.String("provider", req.Provider),
			zap.Error(err))
		response.R(c, response.Response{
			Error: -1,
			Msg:   err.Error(),
			Data:  nil,
		})
		return
	}

	response.R(c, response.Response{
		Error: 0,
		Msg:   "Success",
		Data:  resp,
	})
}

func (h *ChatHandler) handleStreamingChat(c *gin.Context, req dto.ChatRequest) {
	// Set headers for SSE (Server-Sent Events)
	c.Header("Content-Type", "text/event-stream")
	c.Header("Cache-Control", "no-cache")
	c.Header("Connection", "keep-alive")
	c.Header("Access-Control-Allow-Origin", "*")
	c.Header("Access-Control-Allow-Headers", "Cache-Control")

	// Get streaming channel
	streamChan, err := h.chatService.StreamChatCompletion(req)
	if err != nil {
		log.GetLogger().Error("Failed to start streaming chat", zap.Error(err))
		c.SSEvent("error", fmt.Sprintf("Failed to start streaming: %v", err))
		return
	}

	// Stream responses
	flusher, ok := c.Writer.(http.Flusher)
	if !ok {
		log.GetLogger().Error("Streaming not supported")
		c.SSEvent("error", "Streaming not supported")
		return
	}

	for streamResp := range streamChan {
		data, err := json.Marshal(streamResp)
		if err != nil {
			log.GetLogger().Error("Failed to marshal stream response", zap.Error(err))
			continue
		}

		c.SSEvent("data", string(data))
		flusher.Flush()

		if streamResp.Done {
			break
		}
	}

	c.SSEvent("done", "")
}

func (h *ChatHandler) GetProviders(c *gin.Context) {
	providers := h.chatService.GetAvailableProviders()

	response.R(c, response.Response{
		Error: 0,
		Msg:   "Success",
		Data:  providers,
	})
}

func (h *ChatHandler) TestProvider(c *gin.Context) {
	provider := c.Query("provider")
	if provider == "" {
		response.R(c, response.Response{
			Error: -1,
			Msg:   "Provider parameter is required",
			Data:  nil,
		})
		return
	}

	model := c.Query("model")
	testPrompt := "Hello, please respond with 'Test successful' to confirm the connection."

	req := dto.ChatRequest{
		Provider: provider,
		Prompt:   testPrompt,
		Model:    model,
		Stream:   false,
	}

	resp, err := h.chatService.ChatCompletion(req)
	if err != nil {
		log.GetLogger().Error("Provider test failed",
			zap.String("provider", provider),
			zap.Error(err))
		response.R(c, response.Response{
			Error: -1,
			Msg:   fmt.Sprintf("Provider test failed: %v", err),
			Data:  nil,
		})
		return
	}

	response.R(c, response.Response{
		Error: 0,
		Msg:   "Provider test successful",
		Data:  resp,
	})
}

// ChatCompletionSimple provides a simple endpoint that accepts form data
func (h *ChatHandler) ChatCompletionSimple(c *gin.Context) {
	provider := c.PostForm("provider")
	prompt := c.PostForm("prompt")
	model := c.PostForm("model")

	if provider == "" {
		response.R(c, response.Response{
			Error: -1,
			Msg:   "Provider is required",
			Data:  nil,
		})
		return
	}

	if prompt == "" {
		response.R(c, response.Response{
			Error: -1,
			Msg:   "Prompt is required",
			Data:  nil,
		})
		return
	}

	req := dto.ChatRequest{
		Provider: provider,
		Prompt:   prompt,
		Model:    model,
		Stream:   false,
	}

	resp, err := h.chatService.ChatCompletion(req)
	if err != nil {
		log.GetLogger().Error("ChatCompletionSimple failed",
			zap.String("provider", provider),
			zap.Error(err))
		response.R(c, response.Response{
			Error: -1,
			Msg:   err.Error(),
			Data:  nil,
		})
		return
	}

	response.R(c, response.Response{
		Error: 0,
		Msg:   "Success",
		Data:  resp,
	})
}

// ChatCompletionCurl provides a curl-friendly endpoint
func (h *ChatHandler) ChatCompletionCurl(c *gin.Context) {
	// Try to parse as JSON first
	var req dto.ChatRequest
	if err := c.ShouldBindJSON(&req); err == nil {
		// JSON request
		if req.Provider != "" && req.Prompt != "" {
			h.processChatRequest(c, req)
			return
		}
	}

	// Fall back to form data
	provider := c.PostForm("provider")
	prompt := c.PostForm("prompt")
	model := c.PostForm("model")
	streamStr := c.PostForm("stream")

	if provider == "" || prompt == "" {
		response.R(c, response.Response{
			Error: -1,
			Msg:   "Both provider and prompt are required",
			Data:  nil,
		})
		return
	}

	stream := strings.ToLower(streamStr) == "true"

	req = dto.ChatRequest{
		Provider: provider,
		Prompt:   prompt,
		Model:    model,
		Stream:   stream,
	}

	h.processChatRequest(c, req)
}

func (h *ChatHandler) processChatRequest(c *gin.Context, req dto.ChatRequest) {
	if req.Stream {
		h.handleStreamingChat(c, req)
		return
	}

	resp, err := h.chatService.ChatCompletion(req)
	if err != nil {
		log.GetLogger().Error("Chat completion failed",
			zap.String("provider", req.Provider),
			zap.Error(err))
		response.R(c, response.Response{
			Error: -1,
			Msg:   err.Error(),
			Data:  nil,
		})
		return
	}

	response.R(c, response.Response{
		Error: 0,
		Msg:   "Success",
		Data:  resp,
	})
}
