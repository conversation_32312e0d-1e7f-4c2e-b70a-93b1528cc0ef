### 1. Не удается найти файл конфигурации `app.log`, невозможно узнать содержание ошибки
Пользователям Windows рекомендуется разместить рабочую директорию программы в папке, отличной от диска C.

### 2. Файл конфигурации был создан, но возникает ошибка "Не найден файл конфигурации"
Убедитесь, что имя файла конфигурации — `config.toml`, а не `config.toml.txt` или что-то другое. После завершения настройки структура рабочей папки программы должна выглядеть следующим образом:
```
/── config/
│   └── config.toml
├── cookies.txt （<- необязательный файл cookies.txt）
└── krillinai.exe
```

### 3. Заполнены настройки большого модели, но возникает ошибка "xxxxx требует настройки xxxxx API Key"
Хотя модельный сервис и голосовой сервис могут использовать услуги openai, существуют сценарии, когда большая модель использует не openai, поэтому эти две настройки разделены. Кроме настроек большой модели, пожалуйста, найдите настройки whisper ниже и заполните соответствующие ключи и другую информацию.

### 4. Ошибка содержит "yt-dlp error"
Проблема с загрузчиком видео, скорее всего, связана с сетевыми проблемами или проблемами версии загрузчика. Проверьте, включен ли сетевой прокси и правильно ли он настроен в конфигурационном файле, также рекомендуется выбрать узел в Гонконге. Загрузчик автоматически устанавливается программой, источник установки я буду обновлять, но это не официальный источник, поэтому могут быть задержки. Если возникли проблемы, попробуйте обновить вручную, способ обновления:

Откройте терминал в каталоге bin программы и выполните
```
./yt-dlp.exe -U
```
Здесь `yt-dlp.exe` замените на фактическое имя программы ytdlp в вашей системе.

### 5. После развертывания субтитры генерируются нормально, но встроенные субтитры в видео содержат много иероглифов
Чаще всего это связано с отсутствием китайских шрифтов в Linux. Пожалуйста, загрузите шрифты [微软雅黑](https://modelscope.cn/models/Maranello/KrillinAI_dependency_cn/resolve/master/%E5%AD%97%E4%BD%93/msyh.ttc) и [微软雅黑-bold](https://modelscope.cn/models/Maranello/KrillinAI_dependency_cn/resolve/master/%E5%AD%97%E4%BD%93/msyhbd.ttc) (или выберите шрифты, соответствующие вашим требованиям), а затем выполните следующие шаги:
1. Создайте папку msyh в /usr/share/fonts/ и скопируйте загруженные шрифты в этот каталог.
2. 
    ```
    cd /usr/share/fonts/msyh
    sudo mkfontscale
    sudo mkfontdir
    fc-cache
    ```